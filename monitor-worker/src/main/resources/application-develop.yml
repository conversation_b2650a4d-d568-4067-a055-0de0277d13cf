server:
  port: 8082
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************
          username: root
          password: 123456
        postgresql:
          url: *******************************************************
          username: postgres
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos
        password: nacos@jdlX2022
  data:
    redis:
      host: *************
      password: jdlX2022@redis-test
  kafka:
    bootstrap-servers: *************:9092
jmq:
  password: 29c64ca269984eaa9fe6bebc39b69c80
  app: rovermonitorworker
  address: test-nameserver.jmq.jd.local:50088
rover-video:
  snapshotUrl: http://rover-video-process-staging.jd.local/video/process/snapshot/realtime
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
accident:
  url: https://jdxmonitor-beta.jdl.cn/app/accidentmanage
