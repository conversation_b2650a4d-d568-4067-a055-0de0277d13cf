package com.jdx.rover.monitor.worker.quartz;

import com.jdx.rover.monitor.service.web.MonitorAccidentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 定时更新事故bug状态
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AccidentBugSynchronizationJob {

    private final MonitorAccidentService monitorAccidentService;

    @Scheduled(cron = "0 0 * * * ?")
    public void updateBugStatus() {
        monitorAccidentService.updateBugStatus();
    }
}
