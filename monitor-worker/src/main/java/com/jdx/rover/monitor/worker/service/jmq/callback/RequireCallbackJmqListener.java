package com.jdx.rover.monitor.worker.service.jmq.callback;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.message.VehicleRepairInfoMessage;
import com.jdx.rover.monitor.constant.AccidentConstant;
import com.jdx.rover.monitor.dto.accident.AccidentFlowDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.accident.AccidentNewStatusEnum;
import com.jdx.rover.monitor.enums.accident.ThirdVehicleAccidentOperateEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentFlowEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentLogEnum;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentJmqProducer;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.accident.AccidentRecordLogManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentRecordLog;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 处理维修单JMQ消息
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class RequireCallbackJmqListener implements MessageListener {

    private final AccidentManager accidentManager;

    private final JmqProducerService jmqProducerService;

    private final AccidentJmqProducer accidentJmqProducer;

    private final AccidentFlowLogManager accidentFlowLogManager;

    private final AccidentRecordLogManager accidentRecordLogManager;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    private void handleOneMessage(String message) {
        VehicleRepairInfoMessage vehicleRepairInfoMessage = JsonUtils.readValue(message, VehicleRepairInfoMessage.class);
        if (vehicleRepairInfoMessage == null || !Objects.equals(vehicleRepairInfoMessage.getStatus(), 1)) {
            log.info("不是待受理维修单,忽略,requireNumber:{}", vehicleRepairInfoMessage.getRepairNo());
            return;
        }
        if (StringUtils.isBlank(vehicleRepairInfoMessage.getIssueNumber())) {
            log.info("维修单未关联工单,忽略,requireNumber:{}", vehicleRepairInfoMessage.getRepairNo());
            return;
        }
        String issueNumber = vehicleRepairInfoMessage.getIssueNumber();
        String username = vehicleRepairInfoMessage.getOperatorUserName();
        List<Accident> accidentList = accidentManager.selectByIssueNumber(issueNumber);
        if (CollectionUtils.isEmpty(accidentList)) {
            log.info("未找到对应事故,忽略,requireNumber:{},issueNumber:{}", vehicleRepairInfoMessage.getRepairNo(), issueNumber);
            return;
        }
        for (Accident accidentDB : accidentList) {
            String accidentNo = accidentDB.getAccidentNo();
            String cardCallbackLock = AccidentConstant.getCardCallbackLock(accidentNo);
            if (!RedissonUtils.tryLock(cardCallbackLock, 3, TimeUnit.SECONDS)) {
                log.error("处理事故消息获取锁失败,accidentNo:{}", accidentNo);
                continue;
            }
            try {
                Accident accident = accidentManager.selectByAccidentNo(accidentNo);
                if (Objects.equals(accident.getStatus(), AccidentNewStatusEnum.VEHICLE_REPAIR.getValue())) {
                    log.info("事故已经为车辆维修状态,暂不处理,accident={},", JsonUtils.writeValueAsString(accident));
                    continue;
                }
                //更新事故状态
                LambdaUpdateWrapper<Accident> accidentLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                accidentLambdaUpdateWrapper.eq(Accident::getAccidentNo, accidentNo)
                        .set(Accident::getStatus, AccidentNewStatusEnum.VEHICLE_REPAIR.getValue())
                        .set(accident.getRecoverTime() == null, Accident::getRecoverTime, new Date());
                accidentManager.update(accidentLambdaUpdateWrapper);
                //记录事故流程
                AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
                String content = String.format("提报维修，%s于%s，提报维修单", username, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                accidentFlowLog.setAccidentNo(accident.getAccidentNo());
                accidentFlowLog.setUserName(username);
                accidentFlowLog.setSource(AccidentFlowEnum.ACCIDENT_ADD_REQUIRE.getValue());
                accidentFlowLog.setContent(content);
                accidentFlowLog.setCreateUser(username);
                accidentFlowLog.setModifyUser(username);
                accidentFlowLogManager.save(accidentFlowLog);
                //记录事故日志
                AccidentLogEnum accidentLogEnum = AccidentLogEnum.ACCIDENT_ADD_REQUIRE;
                AccidentRecordLog accidentRecordLog = new AccidentRecordLog();
                accidentRecordLog.setAccidentNo(accidentNo);
                accidentRecordLog.setUserName(username);
                accidentRecordLog.setSource(accidentLogEnum.getValue());
                accidentRecordLog.setTitle(accidentLogEnum.getTitle());
                accidentRecordLog.setContent(String.format(accidentLogEnum.getContent(), username, DateUtil.now()));
                accidentRecordLog.setCreateUser(username);
                accidentRecordLog.setModifyUser(username);
                accidentRecordLogManager.save(accidentRecordLog);
                //发送事故消息卡片JMQ
                pushThirdAccidentFlowEvent(accidentNo, AccidentFlowEnum.THIRD_ACCIDENT_REPORT.getValue(), null, ThirdVehicleAccidentOperateEnum.REPAIR.getValue());
                //发送事故变更JMQ
                accidentJmqProducer.sendAccidentInfoProducer(accidentNo);
            } catch (Exception e) {
                log.error("处理事故消息卡片回调发生异常,accidentNo={}", accidentNo, e);
            } finally {
                RedissonUtils.unLock(cardCallbackLock);
            }
        }
    }

    private void pushThirdAccidentFlowEvent(String accidentNo, String accidentFlowEnum, String userName, String operationType) {
        AccidentFlowDTO accidentFlowDTO = new AccidentFlowDTO();
        accidentFlowDTO.setAccidentNo(accidentNo);
        accidentFlowDTO.setAccidentFlowEnum(accidentFlowEnum);
        accidentFlowDTO.setOperator(userName);
        accidentFlowDTO.setOperateType(operationType);
        log.info("发送三方车事故流程JMQ:{}", JsonUtils.writeValueAsString(accidentFlowDTO));
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_ACCIDENT_FLOW_EVENT.getTopic(), accidentFlowDTO);
    }
}
