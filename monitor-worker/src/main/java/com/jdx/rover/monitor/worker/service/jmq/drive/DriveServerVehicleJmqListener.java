/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.worker.service.jmq.drive;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.service.drive.DriveServerVehicleReplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接收平行驾驶指令回复信息
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DriveServerVehicleJmqListener implements MessageListener {
    /**
     * 远程驾驶连接服务
     */
    private final DriveServerVehicleReplyService driveServerVehicleReplyService;

    @Override
    public void onMessage(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        if (messageList.size() > 1) {
            log.info("Received messageSize={}", messageList.size());
        }

        messageList.forEach(message -> {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            try {
                driveServerVehicleReplyService.handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message, e);
            }
        });
    }

}

