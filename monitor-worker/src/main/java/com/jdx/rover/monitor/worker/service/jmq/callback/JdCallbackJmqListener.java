package com.jdx.rover.monitor.worker.service.jmq.callback;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.monitor.constant.AccidentConstant;
import com.jdx.rover.monitor.dto.accident.AccidentFlowDTO;
import com.jdx.rover.monitor.dto.callback.JdCardActionDataDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.accident.AccidentNewStatusEnum;
import com.jdx.rover.monitor.enums.accident.ThirdVehicleAccidentOperateEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentFlowEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentLogEnum;
import com.jdx.rover.monitor.manager.accident.AccidentFlowLogManager;
import com.jdx.rover.monitor.manager.accident.AccidentJmqProducer;
import com.jdx.rover.monitor.manager.accident.AccidentManager;
import com.jdx.rover.monitor.manager.accident.AccidentRecordLogManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentFlowLog;
import com.jdx.rover.monitor.po.AccidentRecordLog;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Service
@Slf4j
public class JdCallbackJmqListener implements MessageListener {

    private final AccidentManager accidentManager;

    private final JmqProducerService jmqProducerService;

    private final AccidentJmqProducer accidentJmqProducer;

    private final AccidentFlowLogManager accidentFlowLogManager;

    private final AccidentRecordLogManager accidentRecordLogManager;

    private final VehicleManager vehicleManager;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    private void handleOneMessage(String message) {
        JdCardActionDataDTO jdCardActionDataDTO = JsonUtils.readValue(message, JdCardActionDataDTO.class);
        if (jdCardActionDataDTO == null || !Objects.equals(jdCardActionDataDTO.getBusinessType(), "accident")) {
            return;
        }
        //判断环境是否一致,不一致直接忽略
        if (!Objects.equals(jdCardActionDataDTO.getEnv(), SpringUtil.getActiveProfile())) {
            log.info("接口京ME回调环境不一致，忽略回调,env={}", jdCardActionDataDTO.getEnv());
            return;
        }
        String accidentNo = jdCardActionDataDTO.getNumber();
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if (accident == null) {
            log.info("处理消息卡片回调未找到对应事故，accidentNo={}", accidentNo);
            return;
        }
        String cardCallbackLock = AccidentConstant.getCardCallbackLock(accidentNo);
        if (!RedissonUtils.tryLock(cardCallbackLock, 3, TimeUnit.SECONDS)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_REPEAT_OPERATION.getCode(), MonitorErrorEnum.ERROR_REPEAT_OPERATION.getMessage());
        }
        try {
            String userName = jdCardActionDataDTO.getUserErp();
            accident = accidentManager.selectByAccidentNo(accidentNo);
            if (!Objects.equals(accident.getStatus(), AccidentNewStatusEnum.INFLUENCE_OPERATION.getValue())) {
                log.info("处理回调,消息卡片状态不为影响运营,不处理,accident={},", JsonUtils.writeValueAsString(accident));
                return;
            }
            //更新事故状态
            LambdaUpdateWrapper<Accident> accidentLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            accidentLambdaUpdateWrapper.eq(Accident::getAccidentNo, accidentNo)
                    .set(Accident::getStatus, AccidentNewStatusEnum.RECOVER_OPERATION.getValue())
                    .set(Accident::getRecoverTime, new Date());
            accidentManager.update(accidentLambdaUpdateWrapper);
            //记录事故流程
            AccidentFlowLog accidentFlowLog = new AccidentFlowLog();
            String content = String.format("恢复运营，%s于%s，操作恢复运营", userName, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            accidentFlowLog.setAccidentNo(accident.getAccidentNo());
            accidentFlowLog.setUserName(userName);
            accidentFlowLog.setSource(AccidentFlowEnum.ACCIDENT_RECOVER.getValue());
            accidentFlowLog.setContent(content);
            accidentFlowLog.setCreateUser(userName);
            accidentFlowLog.setModifyUser(userName);
            accidentFlowLogManager.save(accidentFlowLog);
            //记录事故日志
            AccidentLogEnum accidentLogEnum = AccidentLogEnum.ACCIDENT_RECOVER;
            AccidentRecordLog accidentRecordLog = new AccidentRecordLog();
            accidentRecordLog.setAccidentNo(accidentNo);
            accidentRecordLog.setUserName(userName);
            accidentRecordLog.setSource(accidentLogEnum.getValue());
            accidentRecordLog.setTitle(accidentLogEnum.getTitle());
            accidentRecordLog.setContent(String.format(accidentLogEnum.getContent(), userName, DateUtil.now()));
            accidentRecordLog.setCreateUser(userName);
            accidentRecordLog.setModifyUser(userName);
            accidentRecordLogManager.save(accidentRecordLog);
            //发送事故消息卡片JMQ
            VehicleBasicDTO vehicleBasicDTO = vehicleManager.getBasicByName(accident.getVehicleName());
            if (Objects.equals(vehicleBasicDTO.getSupplier(), SupplierEnum.JD.getValue())) {
                pushAccidentFlowEvent(accidentNo, AccidentFlowEnum.JD_ACCIDENT_EDIT.getValue(), null);
            } else {
                pushThirdAccidentFlowEvent(accidentNo, AccidentFlowEnum.THIRD_ACCIDENT_REPORT.getValue(), null, ThirdVehicleAccidentOperateEnum.RECOVER.getValue());
            }
            //发送事故变更JMQ
            accidentJmqProducer.sendAccidentInfoProducer(accidentNo);
        } catch (Exception e) {
            log.error("处理事故消息卡片回调发生异常,accidentNo={}", accidentNo, e);
        } finally {
            RedissonUtils.unLock(cardCallbackLock);
        }
    }

    private void pushThirdAccidentFlowEvent(String accidentNo, String accidentFlowEnum, String userName, String operationType) {
        AccidentFlowDTO accidentFlowDTO = new AccidentFlowDTO();
        accidentFlowDTO.setAccidentNo(accidentNo);
        accidentFlowDTO.setAccidentFlowEnum(accidentFlowEnum);
        accidentFlowDTO.setOperator(userName);
        accidentFlowDTO.setOperateType(operationType);
        log.info("发送三方车事故流程JMQ:{}", JsonUtils.writeValueAsString(accidentFlowDTO));
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_ACCIDENT_FLOW_EVENT.getTopic(), accidentFlowDTO);
    }

    private void pushAccidentFlowEvent(String accidentNo, String accidentFlowEnum, String userName) {
        AccidentFlowDTO accidentFlowDTO = new AccidentFlowDTO();
        accidentFlowDTO.setAccidentNo(accidentNo);
        accidentFlowDTO.setAccidentFlowEnum(accidentFlowEnum);
        accidentFlowDTO.setOperator(userName);
        log.info("发送自研车事故流程JMQ:{}", JsonUtils.writeValueAsString(accidentFlowDTO));
        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_ACCIDENT_FLOW_EVENT.getTopic(), accidentFlowDTO);
    }
}
