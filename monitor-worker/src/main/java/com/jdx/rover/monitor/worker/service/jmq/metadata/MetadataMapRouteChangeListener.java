/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.message.deployment.DeploymentOpenMapRouteInfoMessage;
import com.jdx.rover.monitor.service.mapcollection.DeploymentMapTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 接收主数据地图路线变更
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetadataMapRouteChangeListener implements MessageListener {

    private final DeploymentMapTaskService deploymentMapTaskService;

    public void onMessage(List<Message> messages) {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理动态地图升级变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并发布到指定主题。
     * @param message 要处理的消息。
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO commonMessageDto = JsonUtils.readValue(message, new TypeReference<CommonMessageDTO>() {
        });
        if (Objects.isNull(commonMessageDto)) {
            return;
        }
        Map<String, Object> mapInfo = commonMessageDto.getExtendedInfo();
        if (MapUtils.isEmpty(mapInfo) || Objects.isNull(mapInfo.get("mapCollectionTask"))) {
            return;
        }
        DeploymentOpenMapRouteInfoMessage mapRouteInfoMessage = JsonUtils.readValue(
                JsonUtils.writeValueAsString(mapInfo.get("mapCollectionTask")), DeploymentOpenMapRouteInfoMessage.class);
        if (Objects.isNull(mapRouteInfoMessage)) {
            return;
        }
        deploymentMapTaskService.syncCollectionTask(mapRouteInfoMessage);
    }
}