/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import cn.hutool.core.collection.CollUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.kafka.CommonMessageDTO;
import com.jdx.rover.metadata.api.domain.enums.kafka.OperationTypeEnum;
import com.jdx.rover.metadata.domain.dto.vehicle.StationVehicleBasicDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.manager.vehicle.MetadataVehicleApiManager;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/18 16:51
 * @description Topic: metadata_station_message
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataStationConsumer implements MessageListener {

    /**
     * VehicleBasicRepository
     */
    private final VehicleBasicRepository vehicleBasicRepository;

    /**
     * MetadataStationApiManager
     */
    private final MetadataVehicleApiManager metadataVehicleApiManager;

    /**
     * MetadataStationApiManager
     */
    private final MetadataStationApiManager metadataStationApiManager;

    @Override
    public void onMessage(List<Message> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (Message message : list) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息，更新车辆基本信息。
     *
     * @param message JSON 格式的消息字符串，表示车辆基本信息的变化。
     */
    private void handleOneMessage(String message) {
        CommonMessageDTO commonMessageDTO = JsonUtils.readValue(message, CommonMessageDTO.class);
        if (commonMessageDTO == null) {
            return;
        }

        OperationTypeEnum operationTypeEnum = OperationTypeEnum.valueOf(commonMessageDTO.getOperation());
        Integer stationId = commonMessageDTO.getId();
        if (stationId == null) {
            return;
        }

        switch (operationTypeEnum) {
            case ADD:
            case EDIT:
                updateStationVehicle(stationId);
                break;
        }
    }

    /**
     * 更新站点下的车辆信息
     *
     * @param stationId 站点ID
     */
    private void updateStationVehicle(Integer stationId) {
        log.info("更新站点[{}]下的车辆信息", stationId);
        List<StationVehicleBasicDTO> localVehicleListByStationId = metadataStationApiManager.getLocalVehicleListByStationId(stationId);
        if (CollUtil.isEmpty(localVehicleListByStationId)) {
            return;
        }
        localVehicleListByStationId.forEach(stationVehicleBasicDTO -> {
            VehicleBasicDTO vehicleBasicDTO = metadataVehicleApiManager.getByName(stationVehicleBasicDTO.getVehicleName());
            if (vehicleBasicDTO != null) {
                vehicleBasicRepository.set(vehicleBasicDTO);
            }
        });
    }
}
