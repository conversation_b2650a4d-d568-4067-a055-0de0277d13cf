package com.jdx.rover.monitor.repository.redis;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.entity.VehiclePncNavigationStopEntity;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 车辆PNC导航停靠点信息缓存
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VehiclePncNavigationStopRepository {

    /**
     * 获取车辆PNC导航停靠点信息
     *
     * @param vehicleName 车辆名称
     * @return 导航停靠点信息列表
     */
    public List<VehiclePncNavigationStopEntity> getByKey(String vehicleName) {
        try {
            String redisKey = RedisKeyEnum.SCHEDULE_PNC_NAVIGATION_STOP_PREFIX.getValue() + vehicleName;
            return RedissonUtils.getObject(redisKey);
        } catch (Exception e) {
            log.error("Get vehicle pnc navigation stop exception for vehicle: {}", vehicleName, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 删除车辆PNC导航停靠点信息
     *
     * @param vehicleName 车辆名称
     */
    public void remove(String vehicleName) {
        String redisKey = RedisKeyEnum.SCHEDULE_PNC_NAVIGATION_STOP_PREFIX.getValue() + vehicleName;
        RedissonUtils.deleteObject(redisKey);
    }

    /**
     * 存储车辆PNC导航停靠点信息
     *
     * @param vehicleName 车辆名称
     * @param navigationStopList 导航停靠点信息列表
     * @return 是否存储成功
     */
    public boolean set(String vehicleName, List<VehiclePncNavigationStopEntity> navigationStopList) {
        String redisKey = RedisKeyEnum.SCHEDULE_PNC_NAVIGATION_STOP_PREFIX.getValue() + vehicleName;
        try {
            remove(redisKey);
            RBucket<Object> rBucket = RedissonUtils.getRBucket(redisKey);
            rBucket.set(navigationStopList);
            rBucket.expire(getRemainingMilliSeconds(), TimeUnit.MILLISECONDS);
            return true;
        } catch (Exception e) {
            log.error("Push vehicle pnc navigation stop error for vehicle: {}", vehicleName, e);
            return false;
        }
    }

    /**
     * 计算当天剩余毫秒数
     *
     * @return 当天剩余毫秒数
     */
    private Long getRemainingMilliSeconds() {
        return Date
                .from(LocalDateTime.now().with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant())
                .getTime()
                - Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }
}
