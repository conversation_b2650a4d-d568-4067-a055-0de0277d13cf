/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.constant.LocalCacheConstant;
import com.jdx.rover.monitor.entity.vehicle.VehicleGuardianAebDO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

/**
 * 车辆安全接管缓存用于车辆遥控
 */
@Repository
public class VehicleGuardianEmergencyRepository {

  static final String VEHICLE_CONTROL_GUARDIAN_AEB = "vehicle_control_guardian_aeb_%s";

  @Cacheable(value = LocalCacheConstant.VEHICLE_CONTROL_GUARDIAN_AEB, key = "#vehicleName")
  public VehicleGuardianAebDO get(String vehicleName) {
    String key = String.format(VEHICLE_CONTROL_GUARDIAN_AEB, vehicleName);
    return RedissonUtils.getObject(key);
  }

  @LocalCacheEvict(value = LocalCacheConstant.VEHICLE_CONTROL_GUARDIAN_AEB, key = "#vehicleName")
  public void save(String vehicleName, VehicleGuardianAebDO vehicleGuardianAebDo) {
    String key = String.format(VEHICLE_CONTROL_GUARDIAN_AEB, vehicleName);
    RedissonUtils.setObject(key, vehicleGuardianAebDo);
  }

  @LocalCacheEvict(value = LocalCacheConstant.VEHICLE_CONTROL_GUARDIAN_AEB, key = "#vehicleName")
  public void delete(String vehicleName) {
    String key = String.format(VEHICLE_CONTROL_GUARDIAN_AEB, vehicleName);
    RedissonUtils.deleteObject(key);
  }

}