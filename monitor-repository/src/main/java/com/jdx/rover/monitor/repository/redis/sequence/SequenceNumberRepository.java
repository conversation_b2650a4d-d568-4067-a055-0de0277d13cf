package com.jdx.rover.monitor.repository.redis.sequence;

import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

/**
 * 序列号生成器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/28
 */
@Repository
@Slf4j
public class SequenceNumberRepository {
    private static final String DRIVE_CONNECT_PREFIX = "drive:connect:number:";
    private static final String DRIVE_VEHICLE_PREFIX = "drive:vehicle:number:";

    /**
     * 获取当日下一个序列号
     *
     * @param vehicleName 车号
     * @return 序列号（1开始递增）
     */
    public int getDriveConnectNumber(String vehicleName) {
        return getDailyNextSequence(DRIVE_CONNECT_PREFIX + vehicleName);
    }

    /**
     * 获取当日下一个序列号
     *
     * @param vehicleName 车号
     * @return 序列号（1开始递增）
     */
    public int getDriveVehicleNumber(String vehicleName) {
        return getDailyNextSequence(DRIVE_VEHICLE_PREFIX + vehicleName);
    }


    /**
     * 获取当日下一个序列号
     *
     * @param key Redis键
     * @return 序列号（1开始递增）
     */
    public int getDailyNextSequence(String key) {
        RAtomicLong rAtomicLong = RedissonUtils.getAtomicLong(key);
        long result = rAtomicLong.incrementAndGet();

        // 如果是当日首次调用，设置过期时间
        if (result == 1L) {
            setDailyExpiration(rAtomicLong);
        }
        return (int) result;
    }

    /**
     * 为计数器设置当日过期时间
     *
     * @param rAtomicLong Redis原子计数器
     */
    private void setDailyExpiration(RAtomicLong rAtomicLong) {
        //计算当天剩余TTL
        long ttlMillis = getTodayRemainingMillis();
        //存储值并设置TTL（若TTL为0，不设置过期时间，避免异常）
        rAtomicLong.expire(Duration.ofMillis(ttlMillis));
    }

    /**
     * 计算当前时间到当天23:59:59的剩余毫秒数（当天有效TTL）
     *
     * @return 剩余毫秒数（若当前已过23:59:59，返回0，避免负数）
     */
    public static long getTodayRemainingMillis() {
        // 1. 获取当前时间（默认系统时区，可指定时区如 ZoneId.of("Asia/Shanghai")）
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        // 2. 构造当天结束时间（23:59:59.999）
        LocalDateTime todayEnd = LocalDateTime.of(
                now.toLocalDate(),  // 当天日期
                LocalTime.MAX       // 23:59:59.999
        );
        // 3. 计算毫秒差（若now > todayEnd，返回1000ms，防止TTL为负数）
        return Math.max(1000, ChronoUnit.MILLIS.between(now, todayEnd));
    }
}
