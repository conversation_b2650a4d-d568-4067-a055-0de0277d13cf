/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.local.cache.autoconfigure.annotation.LocalCacheEvict;
import com.jdx.rover.monitor.dto.vehicle.SingleVehiclePncTaskAndTrafficLightDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/8/27 11:40
 * @description 车辆PNC任务及红绿灯列表
 */
@Service
public class TrafficLightSetRepository {

    /**
     * 缓存名称
     */
    public static final String CACHE_NAME = "trafficLightSet:vehicle:";

    /**
     * 获取redis key
     */
    private static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }

    /**
     * 获取对象
     */
    @Cacheable(value = CACHE_NAME, key = "#vehicleName")
    public SingleVehiclePncTaskAndTrafficLightDTO get(String vehicleName) {
        String redisKey = getKey(vehicleName);
        return RedissonUtils.getObject(redisKey);
    }

    /**
     * 设置对象
     */
    @LocalCacheEvict(value = CACHE_NAME, key = "#vehicleName")
    public void set(String vehicleName, SingleVehiclePncTaskAndTrafficLightDTO singleVehiclePncTaskAndTrafficLightDTO) {
        String redisKey = getKey(vehicleName);
        RedissonUtils.setObject(redisKey, singleVehiclePncTaskAndTrafficLightDTO);
    }
}
