package com.jdx.rover.monitor.repository.redis.metadata;

import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 车辆维修订单
 */
@Service
@Slf4j
public class VehicleRequireRepository {

    /**
     * 缓存名称
     */
    public static final String CACHE_NAME = "monitor:vehicle:require:";

    /**
     * 获取redis key
     */
    public static String getKey(String vehicleName) {
        return CACHE_NAME + vehicleName;
    }

    /**
     * 设置车辆维修状态缓存
     * @param vehicleName 车辆名称
     * @param isUnderRequire 是否处于维修
     * @param expireTime 过期时间
     */
    public void setVehicleIsUnderRequire(String vehicleName, Boolean isUnderRequire,Long expireTime) {
        //获取redisKey
        String redisKey = getKey(vehicleName);
        //设置缓存
        RedissonUtils.setObject(redisKey, isUnderRequire, expireTime);
    }
}