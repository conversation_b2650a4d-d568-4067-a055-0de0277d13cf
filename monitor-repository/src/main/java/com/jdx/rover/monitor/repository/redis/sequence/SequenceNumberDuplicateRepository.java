/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.repository.redis.sequence;

import cn.hutool.core.text.StrPool;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.Duration;

/**
 * 序列号去重Repository
 * <p>
 * 基于Redis实现的分布式序列号去重功能，防止重复处理相同的序列号消息
 * 支持驾驶连接和车辆回复两种业务场景的去重处理
 * </p>
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2025/8/28
 */
@Repository
@Slf4j
public class SequenceNumberDuplicateRepository {
    /**
     * 驾驶连接去重键前缀
     */
    private static final String DRIVE_CONNECT_PREFIX = "drive:connect:numberReply:";
    /**
     * 车辆回复去重键前缀
     */
    private static final String DRIVE_VEHICLE_PREFIX = "drive:vehicle:numberReply:";

    /**
     * 默认过期时间（分钟）
     */
    private static final int DEFAULT_EXPIRE_SECONDS = 40;

    /**
     * 检查驾驶连接消息是否重复
     *
     * @param vehicleName    车辆名称，不能为空
     * @param sequenceNumber 序列号
     */
    public boolean isDuplicateConnect(String vehicleName, int sequenceNumber) {
        String key = getKey(DRIVE_CONNECT_PREFIX, vehicleName, sequenceNumber);
        return isDuplicate(key, sequenceNumber);
    }

    /**
     * 检查车辆回复消息是否重复
     *
     * @param vehicleName    车辆名称，不能为空
     * @param sequenceNumber 序列号
     */
    public boolean isDuplicateVehicleReply(String vehicleName, int sequenceNumber) {
        String key = getKey(DRIVE_VEHICLE_PREFIX, vehicleName, sequenceNumber);
        return isDuplicate(key, sequenceNumber);
    }


    /**
     * 通用重复检查方法
     * <p>
     * 使用Redis的setIfAbsent原子操作实现分布式去重
     * 如果键不存在则设置值并返回false（非重复），如果键已存在则返回true（重复）
     * </p>
     *
     * @param key   Redis键名
     * @param value 要存储的值
     * @return true-重复，false-非重复
     */
    private boolean isDuplicate(String key, int value) {
        return RedissonUtils.getRBucket(key).setIfAbsent(value, Duration.ofSeconds(DEFAULT_EXPIRE_SECONDS));
    }

    /**
     * 后去Redis键名
     *
     * @param prefix         键前缀
     * @param vehicleName    车辆名称
     * @param sequenceNumber 序列号
     * @return 完整的Redis键名
     */
    private String getKey(String prefix, String vehicleName, int sequenceNumber) {
        return prefix + vehicleName + StrPool.COLON + sequenceNumber;
    }
}
