package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.monitor.manager.require.RequireInfoManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.metadata.VehicleRequireRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class VehicleRequireManager {

    @Autowired
    private RequireInfoManager requireInfoManager;

    @Autowired
    private VehicleRequireRepository vehicleRequireRepository;

    /**
     * 检查指定车辆是否正在维修
     * @param vehicleName 车辆名称
     * @return boolean
     */
    public Boolean checkVehicleIsUnderRequireDatabase(String vehicleName) {

        try {
            Boolean requireCache = RedissonUtils.getObject(VehicleRequireRepository.getKey(vehicleName));
            if (requireCache != null) {
                return requireCache;
            } else {
                //查询数据库
                Boolean isUnderRequire = requireInfoManager.CheckRequireStatusByVehicleName(vehicleName);
                //设置缓存
                vehicleRequireRepository.setVehicleIsUnderRequire(vehicleName, isUnderRequire, 600L);
                return isUnderRequire;
            }
        } catch (Exception e) {
            log.error("查询车辆是否有影响运营维修单异常", e);
        }
        return false;
    }
}