package com.jdx.rover.monitor.manager.jdme;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.jdme.AccessToken;
import com.jdx.rover.monitor.dto.jdme.JdmeGroup;
import com.jdx.rover.monitor.dto.jdme.JdmeGroupUser;
import com.jdx.rover.monitor.dto.jdme.JueActionInfo;
import com.jdx.rover.monitor.dto.jdme.JueCardData;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButton;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonBehavior;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonHref;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonText;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtons;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementColumnItem;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementColumnItemElement;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementColumnItemImgElement;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementText;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementTextItem;
import com.jdx.rover.monitor.dto.jdme.JueCardDataEnums;
import com.jdx.rover.monitor.dto.jdme.JueCardDataHeader;
import com.jdx.rover.monitor.dto.jdme.JueCardDataHeaderTitle;
import com.jdx.rover.monitor.dto.jdme.JueCardDataHeaderTitleI18n;
import com.jdx.rover.monitor.dto.jdme.JueCardDataI18n;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfigRobotUser;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentJdmeConfig;
import com.jdx.rover.monitor.po.AccidentJdmeConfigUser;
import com.jdx.rover.monitor.po.AccidentJdmePush;
import com.jdx.rover.monitor.repository.mapper.AccidentJdmeConfigMapper;
import com.jdx.rover.monitor.repository.mapper.AccidentJdmeConfigUserMapper;
import com.jdx.rover.monitor.repository.mapper.AccidentJdmePushMapper;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.shadow.api.domain.dto.ShadowSubscribeEventTaskDTO;
import com.jdx.rover.shadow.api.domain.vo.ShadowSubscribeEventVO;
import com.jdx.rover.shadow.api.jsf.ShadowTrackingEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 京ME消息推送
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AccidentJdmePushManager extends BaseManager<AccidentJdmePushMapper, AccidentJdmePush> {
    @Autowired
    private ShadowTrackingEventService shadowTrackingEventJsfService;
    @Autowired
    private JdmeConfig jdmeConfig;
    @Autowired
    private AccidentJdmePushMapper accidentJdmePushMapper;
    @Autowired
    private AccidentJdmeConfigMapper accidentJdmeConfigMapper;
    @Autowired
    private AccidentJdmeConfigUserMapper accidentJdmeConfigUserMapper;

    public JdmeConfig getLatestConfig() {
        List<AccidentJdmeConfig> configList = accidentJdmeConfigMapper.selectList(Wrappers.lambdaQuery());
        List<AccidentJdmeConfigUser> configUserList = accidentJdmeConfigUserMapper.selectList(Wrappers.lambdaQuery());
        AccidentJdmeConfig config = CollectionUtil.isEmpty(configList) ? null : configList.get(0);

        if(null == config) {
            return jdmeConfig;
        }
        jdmeConfig.getRobot().setFixedGroupId(config.getFixedGroupId());
        jdmeConfig.getRobot().setManualPin(config.getManualPin());
        jdmeConfig.getRobot().setNewGroupOwner(config.getNewGroupOwner());

        if(CollectionUtil.isEmpty(configUserList)) {
            return jdmeConfig;
        }
        List<JdmeConfigRobotUser> memberUserList = new ArrayList<>();
        List<JdmeConfigRobotUser> atUserList = new ArrayList<>();
        for(AccidentJdmeConfigUser configUser : configUserList){
            String type = configUser.getType();

            JdmeConfigRobotUser user = new JdmeConfigRobotUser();
            user.setPin(configUser.getPin());
            user.setNickname(configUser.getNickname());

            if("member".equalsIgnoreCase(type)) {
                memberUserList.add(user);
            } else {
                atUserList.add(user);
            }
        }
        jdmeConfig.getRobot().setNewGroupMembers(memberUserList);
        jdmeConfig.getRobot().setAtUsers(atUserList);
        return jdmeConfig;
    }

    /**
     * 订阅影子事件
     * @param event
     * @return
     */
    public ShadowSubscribeEventTaskDTO subscribeTrackingEventTask(ShadowSubscribeEventVO event) throws Exception {
        log.info("订阅影子事件入参:{}", event);
        HttpResult<ShadowSubscribeEventTaskDTO> httpResult = this.shadowTrackingEventJsfService.subscribeTrackingEventTask(event);
        log.info("订阅影子事件响应:{}", httpResult);
        if (HttpResult.isSuccess(httpResult)) {
            return httpResult.getData();
        }
        throw new Exception(httpResult.getCode() + "-" + httpResult.getMessage());
    }

    /**
     * 根据事件编号查询推送信息
     * @param eventId
     * @return
     */
    public AccidentJdmePush getByEventId(Integer eventId) {
        LambdaQueryWrapper<AccidentJdmePush> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AccidentJdmePush::getShadowEventId, eventId);
        List<AccidentJdmePush> list = accidentJdmePushMapper.selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    /**
     * 根据事故编号查询推送信息
     * @param accidentNo
     * @return
     */
    public AccidentJdmePush getByAccidentNo(String accidentNo) {
        LambdaQueryWrapper<AccidentJdmePush> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AccidentJdmePush::getAccidentNo, accidentNo);
        return accidentJdmePushMapper.selectOne(queryWrapper);
    }

    /**
     * 根据事故群号查询推送信息
     * @param groupId
     * @return
     */
    public AccidentJdmePush getByGroupId(String groupId) {
        LambdaQueryWrapper<AccidentJdmePush> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AccidentJdmePush::getAccidentGroupId, groupId);
        List<AccidentJdmePush> list = accidentJdmePushMapper.selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    /**
     * 更新事故群号
     * @param eventId
     * @param groupId
     * @return
     */
    public int updateGroupId(Integer eventId, String groupId) {
        LambdaUpdateWrapper<AccidentJdmePush> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(AccidentJdmePush::getAccidentGroupId, groupId);
        updateWrapper.eq(AccidentJdmePush::getShadowEventId, eventId);
        return accidentJdmePushMapper.update(updateWrapper);
    }

    /**
     * 获取APP访问TOKEN
     * @return
     * @throws Exception
     */
    public AccessToken getAppAccessToken() throws Exception {
        String key = RedisKeyEnum.JDME_ACCESS_TOKEN.getValue() + "app";
        String appAccessToken = RedissonUtils.getObject(key);
        long expire = RedissonUtils.getKeyExpire(key);
        if(StrUtil.isNotBlank(appAccessToken)) {
            log.info("从缓存中获取到APP访问TOKEN：[{}]，过期时间：[{}]",  appAccessToken, expire);
            return new AccessToken(appAccessToken, (int)expire);
        }

        Map<String, String> params = new HashMap<>();
        params.put("appKey", jdmeConfig.getRobot().getAppKey());
        params.put("appSecret", jdmeConfig.getRobot().getAppSecret());
        String requestParams = JSONUtil.toJsonStr(params);

        HttpRequest request = new HttpRequest(jdmeConfig.getRobot().getHost() + jdmeConfig.getApi().getAppAccessToken());
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("x-stage", jdmeConfig.getRobot().getEnv());
        request.body(requestParams);
        HttpResponse response = request.execute();
        log.info("获取APP访问TOKEN响应：" + response);
        if(null == response) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getAppAccessToken() + "] response is null");
        }
        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if(0 != code) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getAppAccessToken() + "] response msg [" + msg + "]");
        }
        JSONObject responseBodyDataJson = responseBodyJson.getJSONObject("data");
        String accessToken = responseBodyDataJson.getStr("appAccessToken");
        int expireIn = responseBodyDataJson.getInt("expireIn");

        RedissonUtils.setObject(key, accessToken, expireIn - 10 * 60);

        return new AccessToken(accessToken, expireIn);
    }

    /**
     * 获取Team访问TOKEN
     * @return
     * @throws Exception
     */
    public AccessToken getTeamAccessToken() throws Exception {
        String key = RedisKeyEnum.JDME_ACCESS_TOKEN.getValue() + "team";
        String teamAccessToken = RedissonUtils.getObject(key);
        long expire = RedissonUtils.getKeyExpire(key);
        if(StrUtil.isNotBlank(teamAccessToken)) {
            log.info("从缓存中获取到Team访问TOKEN：[{}]，过期时间：[{}]", teamAccessToken, expire);
            return new AccessToken(teamAccessToken, (int)expire);
        }

        AccessToken token = getAppAccessToken();

        Map<String, String> params = new HashMap<>();
        params.put("appAccessToken", token.getAccessToken());
        params.put("openTeamId", jdmeConfig.getRobot().getOpenTeamId());
        String requestParams = JSONUtil.toJsonStr(params);

        HttpRequest request = new HttpRequest(jdmeConfig.getRobot().getHost() + jdmeConfig.getApi().getTeamAccessToken());
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("x-stage", jdmeConfig.getRobot().getEnv());
        request.body(requestParams);
        HttpResponse response = request.execute();
        log.info("获取Team访问TOKEN响应：" + response);
        if(null == response) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getTeamAccessToken() + "] response is null");
        }
        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if(0 != code) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getTeamAccessToken() + "] response msg [" + msg + "]");
        }
        JSONObject responseBodyDataJson = responseBodyJson.getJSONObject("data");
        String accessToken = responseBodyDataJson.getStr("teamAccessToken");
        int expireIn = responseBodyDataJson.getInt("expireIn");

        RedissonUtils.setObject(key, accessToken, expireIn - 10 * 60);

        return new AccessToken(accessToken, expireIn);
    }

    /**
     * 创建京ME群协议封装
     * @return
     * @throws Exception
     */
    public String createGroup(JdmeGroup jdmeGroup)  {
        return createGroupInternal(jdmeGroup, true);
    }

    /**
     * 创建三方车京me群协议封装
     * @param jdmeGroup
     * @return
     */
    public String createThirdVehicleGroup(JdmeGroup jdmeGroup)  {
        return createGroupInternal(jdmeGroup, false);
    }

    /**
     * 创建京ME群的内部实现方法
     * @param jdmeGroup 群组信息
     * @param includeFixedMembers 是否包含固定成员
     * @return 群组ID
     */
    private String createGroupInternal(JdmeGroup jdmeGroup, boolean includeFixedMembers) {
        try {
            AccessToken token = getTeamAccessToken();
            jdmeConfig = getLatestConfig();

            // 构建群主信息
            Map<String, Object> owner = buildGroupOwner(jdmeGroup);

            // 构建群成员列表
            List<Object> members = buildGroupMembers(jdmeGroup, includeFixedMembers);

            // 构建请求参数
            Map<String, Object> params = buildGroupParams(jdmeGroup, owner, members);

            // 发送创建群组请求
            return sendCreateGroupRequest(token, params);

        } catch (Exception e) {
            log.error("创建京ME群异常", e);
        }
        return null;
    }

    /**
     * 构建群主信息
     * @param jdmeGroup 群组信息
     * @return 群主信息Map
     */
    private Map<String, Object> buildGroupOwner(JdmeGroup jdmeGroup) {
        Map<String, Object> owner = new HashMap<>();
        if (null == jdmeGroup.getOwner()) {
            owner.put("app", "ee");
            owner.put("pin", jdmeConfig.getRobot().getNewGroupOwner());
        } else {
            owner.put("app", StrUtil.isNotBlank(jdmeGroup.getOwner().getApp()) ?
                    jdmeGroup.getOwner().getApp() : "ee");
            owner.put("nickName", jdmeGroup.getOwner().getNickname());
            owner.put("pin", jdmeGroup.getOwner().getPin());
        }
        return owner;
    }

    /**
     * 构建群成员列表
     * @param jdmeGroup 群组信息
     * @param includeFixedMembers 是否包含固定成员
     * @return 群成员列表
     */
    private List<Object> buildGroupMembers(JdmeGroup jdmeGroup, boolean includeFixedMembers) {
        List<JdmeGroupUser> memberList = new ArrayList<>(jdmeGroup.getMembers());

        // 添加机器人
        JdmeGroupUser robot = new JdmeGroupUser();
        robot.setApp("robot.dd");
        robot.setPin(jdmeConfig.getRobot().getPin());
        memberList.add(robot);

        // 添加固定成员（仅自研车群组）
        if (includeFixedMembers) {
            List<JdmeConfigRobotUser> fixedMembers = jdmeConfig.getRobot().getNewGroupMembers();
            if (CollectionUtil.isNotEmpty(fixedMembers)) {
                fixedMembers.forEach(m -> {
                    JdmeGroupUser user = new JdmeGroupUser();
                    user.setApp(StrUtil.isEmpty(m.getApp()) ? "ee" : m.getApp());
                    user.setPin(m.getPin());
                    user.setNickname(m.getNickname());
                    memberList.add(user);
                });
            }
        }

        // 构建成员协议格式并去重
        return buildMemberProtocol(memberList);
    }

    /**
     * 构建成员协议格式
     * @param memberList 成员列表
     * @return 协议格式的成员列表
     */
    private List<Object> buildMemberProtocol(List<JdmeGroupUser> memberList) {
        List<Object> members = new ArrayList<>();
        List<String> checkMemberList = new ArrayList<>();

        for (JdmeGroupUser user : memberList) {
            if (checkMemberList.contains(user.getPin())) {
                continue;
            }
            Map<String, String> member = new HashMap<>();
            member.put("app", StrUtil.isEmpty(user.getApp()) ? "ee" : user.getApp());
            member.put("pin", user.getPin());
            members.add(member);
            checkMemberList.add(user.getPin());
        }
        return members;
    }

    /**
     * 构建群组请求参数
     * @param jdmeGroup 群组信息
     * @param owner 群主信息
     * @param members 成员列表
     * @return 请求参数Map
     */
    private Map<String, Object> buildGroupParams(JdmeGroup jdmeGroup, Map<String, Object> owner, List<Object> members) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", jdmeGroup.getName());
        params.put("notice", jdmeGroup.getNotice());
        params.put("avatar", jdmeGroup.getAvatar());
        params.put("intro", jdmeGroup.getIntro());
        params.put("owner", owner);
        params.put("members", members);
        params.put("uniqueKey", jdmeGroup.getUniqueKey());
        return params;
    }

    /**
     * 发送创建群组请求
     * @param token 访问令牌
     * @param params 请求参数
     * @return 群组ID
     * @throws Exception 请求异常
     */
    private String sendCreateGroupRequest(AccessToken token, Map<String, Object> params) throws Exception {
        // 构建请求体
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("appId", jdmeConfig.getRobot().getAppKey());
        bodyMap.put("robotId", jdmeConfig.getRobot().getId());
        bodyMap.put("tenantId", "CN.JD.GROUP");
        bodyMap.put("params", params);
        bodyMap.put("requestId", IdUtil.fastSimpleUUID());
        bodyMap.put("dateTime", System.currentTimeMillis());
        String requestBody = JSONUtil.toJsonStr(bodyMap);

        // 构建请求头
        HttpRequest request = new HttpRequest(jdmeConfig.getRobot().getHost() + jdmeConfig.getApi().getCreateGroup());
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("authorization", "Bearer " + token.getAccessToken());
        request.header("x-stage", jdmeConfig.getRobot().getEnv());
        request.body(requestBody);
        log.info("创建京ME群请求：" + request);

        // 发送请求并处理响应
        HttpResponse response = request.execute();
        log.info("创建京ME群响应：" + response);
        if (null == response) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getCreateGroup() + "] response is null");
        }

        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if (0 != code) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getCreateGroup() + "] response msg [" + msg + "]");
        }

        JSONObject responseData = responseBodyJson.getJSONObject("data");
        String groupId = responseData.getStr("groupId");
        log.info("创建京ME群新群号：" + groupId);
        return groupId;
    }

    /**
     * 将指定成员列表添加到指定群中
     * @param memberList
     * @param groupId
     * @return
     */
    public boolean addGroupMember(List<JdmeGroupUser> memberList, String groupId) {
        try {
            AccessToken token = getTeamAccessToken();

            // 构建成员表示的协议（复用已有方法）
            List<Object> members = buildMemberProtocol(memberList);

            //内容
            Map<String, Object> params = new HashMap<>();
            params.put("robotId", jdmeConfig.getRobot().getId());//机器人ID
            params.put("groupId", groupId);//群ID
            params.put("members", members);//新增成员

            //构建请求体
            Map<String, Object> bodyMap = new HashMap<>();
            bodyMap.put("appId", jdmeConfig.getRobot().getAppKey());
            bodyMap.put("robotId", jdmeConfig.getRobot().getId());
            bodyMap.put("params", params);
            bodyMap.put("requestId", IdUtil.fastSimpleUUID());
            bodyMap.put("dateTime", System.currentTimeMillis());
            String requestBody = JSONUtil.toJsonStr(bodyMap);

            //构建请求头
            HttpRequest request = new HttpRequest(jdmeConfig.getRobot().getHost() + jdmeConfig.getApi().getAddGroupMember());
            request.setMethod(Method.POST);
            request.header("Content-Type", "application/json; charset=utf-8");
            request.header("authorization", "Bearer " + token.getAccessToken());
            request.header("x-stage", jdmeConfig.getRobot().getEnv());
            request.body(requestBody);
            log.info("添加群成员请求：" + request);

            //发送请求并处理响应
            HttpResponse response = request.execute();
            log.info("添加群成员响应：" + response);
            if (null == response) {
                throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getAddGroupMember() + "] response is null");
            }
            String responseBody = response.body();
            JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
            int code = responseBodyJson.getInt("code");
            String msg = responseBodyJson.getStr("msg");
            if (0 != code) {
                throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getAddGroupMember() + "] response msg [" + msg + "]");
            }
        } catch (Exception e) {
            log.error("添加群成员发生异常", e);
            return false;
        }
        return true;
    }

    /**
     * 获取艾特用户列表
     * @param users
     * @return
     */
    private List<Map<String, Object>> genAtUser(List<JdmeConfigRobotUser> users) {
        List<Map<String, Object>> userList = new ArrayList<>();
        if(null == users) {
            return userList;
        }
        for(JdmeConfigRobotUser user : users) {
            Map<String, Object> paramsBodyUser = new HashMap<>();
            paramsBodyUser.put("app", "ee");
            paramsBodyUser.put("pin", user.getPin());
            paramsBodyUser.put("nickname", user.getNickname());
            userList.add(paramsBodyUser);
        }
        return userList;
    }

    /**
     * 构建分隔行
     * @return
     */
    private Map<String, Object> buildHr() {
        Map<String, Object> hr = new HashMap<>();
        hr.put("tag", "hr");
        return hr;
    }

    /**
     * 构建卡片元素内容
     * @param textItem
     * @return
     */
    private String buildElementText(JueCardDataElementTextItem textItem) {
        if(null == textItem) {
            return null;
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("**");
        stringBuffer.append(textItem.getLabel());
        stringBuffer.append("：** ");
        if(JueCardDataEnums.ElementValueType.TEXT.equals(textItem.getValueType())) {
            stringBuffer.append(textItem.getValue());
        } else if(JueCardDataEnums.ElementValueType.LINK.equals(textItem.getValueType())) {
            stringBuffer.append("[");
            stringBuffer.append(textItem.getValueDesc());
            stringBuffer.append("]");
            stringBuffer.append("(");
            stringBuffer.append(textItem.getValue());
            stringBuffer.append(")");
        }
        return stringBuffer.toString();
    }

    /**
     * 构建卡片中的按钮
     * @param button
     * @return
     */
    private Map<String, Object> buildButton(JueCardDataButton button) {
        Map<String, Object> buttonTextMap = buildButtonText(button);

        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("type", button.getType());
        buttonMap.put("enable", button.isEnable());
        buttonMap.put("text", buttonTextMap);

        JueCardDataButtonBehavior behavior = button.getBehavior();
        JueCardDataButtonHref href = button.getHref();
        if(null != behavior) {
            Map<String, Object> behaviorMap = buildButtonBehavior(behavior);
            buttonMap.put("behavior", behaviorMap);
        }

        JueActionInfo actionInfo = button.getAction();
        if(Objects.nonNull(actionInfo)) {
            buttonMap.put("action", actionInfo);
        }

        Map<String, String> hrefMap = new HashMap<>();
        if(null != href) {
            hrefMap.put("pc", href.getPc());
            hrefMap.put("mobile", href.getMobile());
            hrefMap.put("pc_deeplink", href.getPcDeeplink());
        } else {
            hrefMap.put("pc", "");
            hrefMap.put("mobile", "");
            hrefMap.put("pc_deeplink", "");
        }
        buttonMap.put("href", hrefMap);

        return buttonMap;
    }

    private static Map<String, Object> buildButtonBehavior(JueCardDataButtonBehavior behavior) {
        Map<String, Object> behaviorMap = new HashMap<>();

        if (JueCardDataEnums.ButtonBehaviorMethod.JOIN_GROUP_CHAT.equals(behavior.getMethod())) {
            behaviorMap.put("method", JueCardDataEnums.ButtonBehaviorMethod.JOIN_GROUP_CHAT.getCode());
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("gid", behavior.getParams());
            behaviorMap.put("params", paramsMap);
        } else if (JueCardDataEnums.ButtonBehaviorMethod.SEND_MESSAGE.equals(behavior.getMethod())) {
            behaviorMap.put("method", JueCardDataEnums.ButtonBehaviorMethod.SEND_MESSAGE.getCode());
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("content", behavior.getParams());
            behaviorMap.put("params", paramsMap);
        }
        return behaviorMap;
    }

    private Map<String, Object> buildButtonText(JueCardDataButton button) {
        JueCardDataButtonText buttonText = button.getText();
        JueCardDataElementTextItem textContent = buttonText.getContent();
        String content = textContent.getLabel();

        Map<String, Object> buttonTextMap = new HashMap<>();
        buttonTextMap.put("content", content);

        JueCardDataI18n i18n = buttonText.getI18n();
        if(null != i18n) {
            JueCardDataElementTextItem i18nEnUs = i18n.getEnUs();
            JueCardDataElementTextItem i18nZhCn = i18n.getZhCn();
            String enUs = i18nEnUs.getLabel();
            String zhCn = i18nZhCn.getLabel();

            Map<String, String> i18nMap = new HashMap<>();
            i18nMap.put("en_us", enUs);
            i18nMap.put("zh_cn", zhCn);
            buttonTextMap.put("i18n", i18nMap);
        }
        return buttonTextMap;
    }

    /**
     * 构建卡片文本内容
     * @param textElement
     * @return
     */
    private Map<String, Object> buildElementText(JueCardDataElementText textElement) {
        Map<String, Object> element = new HashMap<>();
        JueCardDataElementTextItem textContent = textElement.getContent();
        String content = buildElementText(textContent);

        element.put("tag", "me_md");
        element.put("content", content);

        JueCardDataI18n i18n = textElement.getI18n();
        Map<String, String> i18nMap = new HashMap<>();
        if(null != i18n) {
            JueCardDataElementTextItem i18nEnUs = i18n.getEnUs();
            JueCardDataElementTextItem i18nZhCn = i18n.getZhCn();

            String enUs = buildElementText(i18nEnUs);
            String zhCn = buildElementText(i18nZhCn);

            i18nMap.put("en_us", enUs);
            i18nMap.put("zh_cn", zhCn);
        } else {
            i18nMap.put("en_us", content);
            i18nMap.put("zh_cn", content);
        }
        element.put("i18n", i18nMap);

        return element;
    }

    /**
     * 构建卡片文本内容
     * @param textElements
     * @return
     */
    private Map<String, Object> buildElementText(List<JueCardDataElementText> textElements) {
        Map<String, Object> element = new HashMap<>();
        element.put("tag", "me_md");

        String content = textElements.stream()
               .map(JueCardDataElementText::getContent)
               .map(this::buildElementText)
               .collect(Collectors.joining("\n"));

        element.put("content", content);

        return element;
    }

    /**
     * 构建卡片按钮
     * @param buttonElement
     * @return
     */
    private Map<String, Object> buildElementButton(JueCardDataButtons buttonElement) {
        Map<String, Object> element = new HashMap<>();
        List<JueCardDataButton> buttonList = buttonElement.getButtons();

        element.put("tag", "buttons");
        if(null == buttonElement.getLayout()) {
            element.put("layout", JueCardDataEnums.ButtonLayout.ROW.getCode());
        } else {
            element.put("layout", buttonElement.getLayout().getCode());
        }
        List<Map<String, Object>> buttons = new ArrayList<>();
        for(JueCardDataButton button : buttonList) {
            Map<String, Object> buttonMap = buildButton(button);
            buttons.add(buttonMap);
        }
        element.put("buttons", buttons);

        return element;
    }

    /**
     * 构建卡片数据
     * @param jueCardData
     * @return
     */
    private Map<String, Object> buildCardData(JueCardData jueCardData) {
        //卡片头
        JueCardDataHeader header = jueCardData.getHeader();
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("theme", header.getTheme());

        JueCardDataHeaderTitle title = header.getTitle();
        Map<String, Object> headerTitleMap = new HashMap<>();
        headerTitleMap.put("tag", "plain_text");
        headerTitleMap.put("content", title.getContent());

        JueCardDataHeaderTitleI18n titleI18n = title.getI18n();
        Map<String, String> titleI18NMap = new HashMap<>();
        if(null != titleI18n) {
            titleI18NMap.put("en_us", titleI18n.getEnUs());
            titleI18NMap.put("zh_cn", titleI18n.getZhCn());
        } else {
            titleI18NMap.put("en_us", title.getContent());
            titleI18NMap.put("zh_cn", title.getContent());
        }
        headerTitleMap.put("i18n", titleI18NMap);
        headerMap.put("title", headerTitleMap);

        //卡片体
        List<Map<String, Object>> elements = new ArrayList<>();
        //分隔行
        if(JueCardDataEnums.LabelColorType.GRAY.getCode().equals(header.getTheme())) {
            elements.add(buildHr());
        }
        //增加事故快照
        if (CollectionUtil.isNotEmpty(jueCardData.getSnapshotUrlList())) {
            Map<String, Object> snapshotTitleMap = new HashMap<>();
            snapshotTitleMap.put("tag", "me_md");
            snapshotTitleMap.put("content", "**事故快照：**前-后-左-右");
            elements.add(snapshotTitleMap);
            List<JueCardDataElementColumnItem> columns = new ArrayList<>();
            JueCardDataElementColumnItem<JueCardDataElementColumnItemElement> jueCardDataElementColumnItem = new JueCardDataElementColumnItem<>();
            for (String snapshotUrl : jueCardData.getSnapshotUrlList()) {
                JueCardDataElementColumnItem<JueCardDataElementColumnItemImgElement> item = new JueCardDataElementColumnItem<>();
                item.setPadding("4px 4px 4px 4px");
                List<JueCardDataElementColumnItemImgElement> jueCardDataElementColumnItemImgElements = new ArrayList<>();
                JueCardDataElementColumnItemImgElement jueCardDataElementColumnItemImgElement = new JueCardDataElementColumnItemImgElement();
                jueCardDataElementColumnItemImgElement.setPreview(true);
                jueCardDataElementColumnItemImgElement.setImg_url(snapshotUrl);
                jueCardDataElementColumnItemImgElement.setScale(1.5);
                jueCardDataElementColumnItemImgElement.setTag("img");
                jueCardDataElementColumnItemImgElements.add(jueCardDataElementColumnItemImgElement);
                item.setElements(jueCardDataElementColumnItemImgElements);
                item.setWeight(1);
                item.setTag("column");
                jueCardDataElementColumnItem.setTag("column");
                columns.add(item);
            }
            Map<String, Object> snapshotMap = new HashMap<>();
            snapshotMap.put("columns", columns);
            snapshotMap.put("tag", "column_set");
            elements.add(snapshotMap);
        }
        //文本提示
        List<JueCardDataElementText> elementList = jueCardData.getElements();
        Map<String, Object> elementMap = buildElementText(elementList);
        elements.add(elementMap);
        //分隔行
        elements.add(buildHr());
        //按钮
        JueCardDataButtons bottons = jueCardData.getButtons();
        elements.add(buildElementButton(bottons));

        //消息卡片
        Map<String, Object> cardDataMap = new HashMap<>();
        cardDataMap.put("header", headerMap);
        cardDataMap.put("elements", elements);

        return cardDataMap;
    }

    /**
     * 发送互动卡片消息协议封装
     * @param jueCardData
     * @throws Exception
     */
    public void sendJUEMsg(JueCardData jueCardData) throws Exception {
        AccessToken token = getTeamAccessToken();
        Map<String, Object> cardDataMap = buildCardData(jueCardData);
        String summary = jueCardData.getSummary();

        //卡片转发配置
        Map<String, Object> forwardMap = new HashMap<>();
        forwardMap.put("reload", false);
        forwardMap.put("summary", summary); //摘要信息
        forwardMap.put("cardData", cardDataMap);
        //消息体
        Map<String, Object> paramsBody = new HashMap<>();
        paramsBody.put("templateId", jdmeConfig.getRobot().getCardTemplateId()); //预配置好的JUE模板ID，从开发者后台创建卡片后获得
        paramsBody.put("templateType", 1); //模板类型 1:普通
        paramsBody.put("summary", summary); //摘要信息
        paramsBody.put("cardData", cardDataMap); //卡片数据，业务自行定义，接口透传
        paramsBody.put("forward", forwardMap);//卡片转发配置，卡片消息转发时携带此字段，可以处理业务参数

        //发送内容
        Map<String, Object> params = new HashMap<>();
        params.put("robotId", jdmeConfig.getRobot().getId()); //机器人ID，创建机器人后获得
        params.put("data", paramsBody); //消息体

        //请求体
        Map<String, Object> body = new HashMap<>();
        body.put("appId", jdmeConfig.getRobot().getAppKey()); //开放平台注册的应用appkey，创建应用后获得
        body.put("erp", null); //目的地ERP，erp和tenantId是一组信息。目的地要么是erp要么是群号，必须二选一
        body.put("tenantId", "CN.JD.GROUP"); //枚举类型（CN.JD.GROUP，TH.JD.GROUP，ID.JD.GROUP，SF.JD.GROUP），分别为（国内、泰国、印尼、赛夫）租户
        body.put("groupId", jueCardData.getGroupId()); //目的地群号，京ME群号
        body.put("requestId", IdUtil.fastUUID()); //请求UUID，不能重复
        body.put("dateTime", System.currentTimeMillis()); //请求时间戳
        body.put("params", params); //发送内容
        String requestParams = JSONUtil.toJsonStr(body);
        log.info("发送卡片请求:{}", requestParams);

        //构建请求头
        HttpRequest request = new HttpRequest(jdmeConfig.getRobot().getHost() + jdmeConfig.getApi().getSendJueMsg());
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("authorization", "Bearer " + token.getAccessToken());
        request.header("x-stage", jdmeConfig.getRobot().getEnv());
        request.body(requestParams);
        log.info("发送互动卡片消息请求：" + request);
        HttpResponse response = request.execute();
        log.info("发送互动卡片消息响应：" + response);
        if(null == response) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getSendJueMsg() + "] response is null");
        }
        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if(0 != code) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getSendJueMsg() + "] response msg [" + msg + "]");
        }
    }

    /**
     * 发送互动卡片消息协议封装
     * @param message
     * @throws Exception
     */
    public void sendJUEMsg(JSONObject message, String summary, String groupId) throws Exception {
        AccessToken token = getTeamAccessToken();

        //卡片转发配置
        Map<String, Object> forwardMap = new HashMap<>();
        forwardMap.put("reload", false);
        forwardMap.put("cardData", message);
        forwardMap.put("summary", summary); //摘要信息

        //消息体
        Map<String, Object> paramsBody = new HashMap<>();
        paramsBody.put("templateId", jdmeConfig.getRobot().getCardTemplateId()); //预配置好的JUE模板ID，从开发者后台创建卡片后获得
        paramsBody.put("templateType", 1); //模板类型 1:普通
        paramsBody.put("summary", summary); //摘要信息
        paramsBody.put("cardData", message); //卡片数据，业务自行定义，接口透传
        paramsBody.put("forward", forwardMap);//卡片转发配置，卡片消息转发时携带此字段，可以处理业务参数

        //发送内容
        Map<String, Object> params = new HashMap<>();
        params.put("robotId", jdmeConfig.getRobot().getId()); //机器人ID，创建机器人后获得
        params.put("data", paramsBody); //消息体

        //请求体
        Map<String, Object> body = new HashMap<>();
        body.put("appId", jdmeConfig.getRobot().getAppKey()); //开放平台注册的应用appkey，创建应用后获得
        body.put("erp", null); //目的地ERP，erp和tenantId是一组信息。目的地要么是erp要么是群号，必须二选一
        body.put("tenantId", "CN.JD.GROUP"); //枚举类型（CN.JD.GROUP，TH.JD.GROUP，ID.JD.GROUP，SF.JD.GROUP），分别为（国内、泰国、印尼、赛夫）租户
        body.put("groupId", groupId); //目的地群号，京ME群号
        body.put("requestId", IdUtil.fastUUID()); //请求UUID，不能重复
        body.put("dateTime", System.currentTimeMillis()); //请求时间戳
        body.put("params", params); //发送内容
        String requestParams = JSONUtil.toJsonStr(body);
        log.info("发送卡片请求:{}", requestParams);

        //构建请求头
        HttpRequest request = new HttpRequest(jdmeConfig.getRobot().getHost() + jdmeConfig.getApi().getSendJueMsg());
        request.setMethod(Method.POST);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("authorization", "Bearer " + token.getAccessToken());
        request.header("x-stage", jdmeConfig.getRobot().getEnv());
        request.body(requestParams);
        log.info("发送互动卡片消息请求：" + request);
        HttpResponse response = request.execute();
        log.info("发送互动卡片消息响应：" + response);
        if(null == response) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getSendJueMsg() + "] response is null");
        }
        String responseBody = response.body();
        JSONObject responseBodyJson = JSONUtil.parseObj(responseBody);
        int code = responseBodyJson.getInt("code");
        String msg = responseBodyJson.getStr("msg");
        if(0 != code) {
            throw new RuntimeException("Access interface [" + jdmeConfig.getApi().getSendJueMsg() + "] response msg [" + msg + "]");
        }
    }
}
