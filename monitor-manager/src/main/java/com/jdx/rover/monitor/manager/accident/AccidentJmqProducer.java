package com.jdx.rover.monitor.manager.accident;

import cn.hutool.core.date.DateUtil;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.dto.AccidentMessageDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.mobile.NewAccidentLevelEnum;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.po.AccidentDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 事故相关JMQ发送
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AccidentJmqProducer {

    private final Producer producer;

    private final AccidentManager accidentManager;

    private final AccidentDetailManager accidentDetailManager;


    public void sendAccidentInfoProducer(String accidentNo) {
        Accident accident = accidentManager.selectByAccidentNo(accidentNo);
        if (accident == null) {
            log.error("发送事故JMQ,事故不存在,accidentNo:{}", accidentNo);
            return;
        }
        AccidentDetail accidentDetail = accidentDetailManager.selectByAccidentNo(accidentNo);
        if (accidentDetail == null) {
            log.error("发送事故JMQ,事故详情不存在,accidentNo:{}", accidentNo);
            return;
        }
        AccidentMessageDTO accidentMessageDTO = new AccidentMessageDTO();
        accidentMessageDTO.setAccidentNo(accidentNo);
        accidentMessageDTO.setVehicleName(accident.getVehicleName());
        accidentMessageDTO.setStationName(accident.getStationName());
        accidentMessageDTO.setSupplier(accident.getSupplier());
        if (Objects.nonNull(accident.getAccidentReportTime())) {
            accidentMessageDTO.setReportTime(accident.getAccidentReportTime());
        } else {
            accidentMessageDTO.setReportTime(accident.getAccidentStartTime());
        }
        accidentMessageDTO.setRecoverTime(accident.getRecoverTime());
        accidentMessageDTO.setAccidentLevel(accidentDetail.getNewAccidentLevel());
        accidentMessageDTO.setAccidentNumLevel(NewAccidentLevelEnum.getAlisaByValue(accidentDetail.getNewAccidentLevel()));
        accidentMessageDTO.setAccidentType(accidentDetail.getSafetyGroupAccidentType());
        accidentMessageDTO.setWhetherIncludeStatistics(accident.getWhetherIncludeStatistics());
        log.info("发送事故变化JMQ,内容:{}", JsonUtils.writeValueAsString(accidentMessageDTO));
        sendMessage(JmqProducerTopicEnum.MONITOR_ACCIDENT_BASIC_INFO.getTopic(), accidentMessageDTO);
    }


    public void sendMessage(String topic, Object data) {
        try {
            Message message = new Message();
            message.setTopic(topic);
            message.setText(data instanceof String? (String)data : JsonUtils.writeValueAsString(data));
            message.setSendTime(System.currentTimeMillis());
            message.setBusinessId(String.valueOf(DateUtil.thisSecond()));
            producer.send(message);
            log.info("发送Jmq消息主题{}, 内容{}", topic, data);
        } catch (Exception e) {
            log.error("发送Jmq消息主题{}, 内容{}触发异常", topic, data, e);
            throw new RuntimeException(e);
        }
    }}
