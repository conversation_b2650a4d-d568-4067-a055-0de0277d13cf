/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.manager.drive;

import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.proto.ProtoUtils;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.enums.drive.error.DriveErrorEnum;
import com.jdx.rover.monitor.enums.mqtt.MqttTopicEnum;
import com.jdx.rover.monitor.enums.vehicle.operate.AllowOperateEnum;
import com.jdx.rover.monitor.manager.vehicle.TakeOverManager;
import com.jdx.rover.monitor.repository.redis.sequence.SequenceNumberRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import com.jdx.rover.server.api.domain.vo.mqtt.MqttMessageVO;
import com.jdx.rover.server.api.service.mqtt.MqttSendJsfService;
import jdx.rover.remote.drive.proto.DriveHeader;
import jdx.rover.remote.drive.server.proto.DriveServerVehicle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 远程驾驶连接服务
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerToVehicleManager {
    /**
     * 发送mqtt消息
     */
    private final MqttSendJsfService mqttSendJsfService;
    /**
     * 监控车辆接管状态
     */
    private final VehicleTakeOverRepository vehicleTakeOverRepository;
    /**
     * 序列号生成器
     */
    private final SequenceNumberRepository sequenceNumberRepository;

    /**
     * 发送连接mqtt消息
     */
    public HttpResult<?> sendCommand(DriveRemoteCommandVO vo) {
        VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverRepository.get(vo.getVehicleName());
        HttpResult<?> validateResult = validateCommand(vo, vehicleTakeOverEntity);
        if (validateResult != null) {
            return validateResult;
        }

        DriveHeader.RequestHeader.Builder requestHeaderBuilder = DriveHeader.RequestHeader.newBuilder();
        requestHeaderBuilder.setRequestTime(System.currentTimeMillis());
        requestHeaderBuilder.setRequestId(RequestIdUtils.getRequestId());
        requestHeaderBuilder.setClientName(vo.getCockpitNumber());
        requestHeaderBuilder.setNeedResponse(true);
        requestHeaderBuilder.setRetry(false);
        requestHeaderBuilder.setSequenceNumber(sequenceNumberRepository.getDriveVehicleNumber(vo.getCockpitNumber()));


        DriveServerVehicle.ServerToVehicle.Builder infoBuilder = DriveServerVehicle.ServerToVehicle.newBuilder();
        DriveRemoteCommandTypeEnum commandType = DriveRemoteCommandTypeEnum.valueOf(vo.getRemoteCommandType());
        switch (commandType) {
            case REMOTE_VELOCITY_CONTROL -> {
                infoBuilder.setMessageType(DriveServerVehicle.ServerToVehicle.MessageType.MAX_VELOCITY);
                infoBuilder.setMaxVelocity(vo.getData());
            }
            case MAX_TORQUE -> {
                infoBuilder.setMessageType(DriveServerVehicle.ServerToVehicle.MessageType.MAX_TORQUE);
                infoBuilder.setMaxTorque(vo.getData());
            }
            default -> {
            }
        }
        infoBuilder.setVehicleName(vo.getVehicleName());
        infoBuilder.setCockpitName(vo.getCockpitNumber());
        infoBuilder.setRequestHeader(requestHeaderBuilder);
        DriveServerVehicle.ServerToVehicle info = infoBuilder.build();

        MqttMessageVO<byte[]> mqttMessageVO = new MqttMessageVO<>();
        mqttMessageVO.setTopic(MqttTopicEnum.DRIVE_SERVER_VEHICLE.getTopic() + vo.getVehicleName());
        mqttMessageVO.setMessage(info.toByteArray());
        mqttSendJsfService.sendBytes(mqttMessageVO);

        mqttMessageVO.setTopic(MqttTopicEnum.SERVER_VEHICLE.getTopic() + vo.getCockpitNumber() + "/" + vo.getVehicleName());
        mqttSendJsfService.sendBytes(mqttMessageVO);
        log.info("服务端发送车端mqtt数据topic={},data={}", mqttMessageVO.getTopic(), ProtoUtils.protoToJson(info));
        return HttpResult.success();
    }

    /**
     * 校验命令是否可以执行
     */
    private static HttpResult<?> validateCommand(DriveRemoteCommandVO driveRemoteCommandVO, VehicleTakeOverEntity vehicleTakeOverEntity) {
        String allowTakeOver = TakeOverManager.getAllowTakeOver(driveRemoteCommandVO.getCockpitNumber(), vehicleTakeOverEntity);
        if (!Objects.equals(allowTakeOver, AllowOperateEnum.EXIT.name())) {
            String msg = String.format(DriveErrorEnum.FORBID_NOT_TAKE_OVER.getTitle(), driveRemoteCommandVO.getCockpitUserName()
                    , driveRemoteCommandVO.getCockpitNumber(), driveRemoteCommandVO.getVehicleName());
            log.info(msg);
            return HttpResult.error(DriveErrorEnum.FORBID_NOT_TAKE_OVER.getValue(), msg);
        }
        return null;
    }
}
