/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.manager.hanlp;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.dictionary.CustomDictionary;
import com.hankcs.hanlp.seg.common.Term;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Hanlp语义解析
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Service
@Slf4j
public class HanlpManager {

  static {
    // 添加自定义领域词典
    CustomDictionary.add("自然语言处理");
    CustomDictionary.add("深度学习框架");
    CustomDictionary.add("自动驾驶系统");
    CustomDictionary.add("高精度地图");
  }


  /**
   * 从给定文本内容中提取关键词列表
   * @param content 待提取关键词的文本内容，若为空则返回空列表
   * @return 提取后的关键词列表（按权重逆序排列，排除单字词，数量为3-10个自适应）
   */
  public List<String> extractKeywords(String content, List<String> tagList) {
    if (StringUtils.isBlank(content)) {
      return Collections.emptyList();
    }
    // 计算纯文字长度（去空格和标点）
    int length = content.replaceAll("\\s+", "").replaceAll("\\pP", "").length();
    int keywordCount = Math.max(5, Math.min(15, length / 8 + 1));
    List<String> keywords = HanLP.extractKeyword(content, keywordCount).stream().filter(k -> k.length() > 1).collect(Collectors.toList());
    keywords.addAll(extractWords(content));
    tagList.stream().filter(StringUtils::isNotBlank).filter(tag -> content.contains(tag)).forEach(keywords::add);
    return keywords.stream().distinct().collect(Collectors.toList());
  }

  /**
   * 从给定文本内容中提取短语列表
   * @param content 待提取关键词的文本内容，若为空则返回空列表
   * @return 提取后的关键词列表（按权重逆序排列，排除单字词，数量为3-10个自适应）
   */
  public List<String> extractWords(String content) {
    if (StringUtils.isBlank(content)) {
      return Collections.emptyList();
    }
    // 计算纯文字长度（去空格和标点）
    int length = content.replaceAll("\\s+", "").replaceAll("\\pP", "").length();
    int keywordCount = Math.max(3, Math.min(10, length / 8 + 1));
    // 提取短语;
    // Java中加载自定义词典
    HanLP.Config.CustomDictionaryPath = new String[]{"data/dictionary/custom/CustomDictionary.txt"};
    CustomDictionary.reload(); // 重载词典
    List<String> keywords = HanLP.extractPhrase(content, keywordCount).stream().filter(k -> k.length() > 1).collect(Collectors.toList());
    return keywords;
  }

  public static boolean fuzzyMatchWithHanLP(String input, String tag, double threshold) {
    // 使用HanLP对输入文本和标签进行分词
    Set<String> inputWords = segmentToWordSet(input);
    System.out.println(Arrays.toString(inputWords.toArray()));
    Set<String> tagWords = segmentToWordSet(tag);
    System.out.println(Arrays.toString(tagWords.toArray()));

    if (tagWords.isEmpty()) {
      return false;
    }

    // 计算交集
    Set<String> intersection = inputWords.stream()
            .filter(tagWords::contains)
            .collect(Collectors.toSet());

    // 计算匹配比例
    double matchRatio = (double) intersection.size() / tagWords.size();

    return matchRatio >= threshold;
  }

  /**
   * 使用HanLP分词并将结果转为词集合
   * @param text 输入文本
   * @return 分词后的词语集合
   */
  private static Set<String> segmentToWordSet(String text) {
    List<Term> termList = HanLP.segment(text);
    return termList.stream()
            .map(term -> term.word)
            .collect(Collectors.toSet());
  }

  /**
   * 计算两个文档关键词列表的TF-IDF余弦相似度
   */
  public static Double countWords(String currentKey, List<String> compareKeys) {
    Optional<Double> values =
            compareKeys.stream().map(compare -> cosineSimilarity(currentKey, compare)).max(Double::compare);
    return values.orElse(0.0);
  }

  /**
   * 计算两个向量的余弦相似度
   * @return 两个向量的余弦相似度值，范围为[-1,1]
   */
  public static double cosineSimilarity(String str1, String str2) {
    // 1. 获取所有字符集合
    Set<Character> chars1 = str1.chars().mapToObj(c -> (char)c).collect(Collectors.toSet());
    Set<Character> chars2 = str2.chars().mapToObj(c -> (char)c).collect(Collectors.toSet());

    // 2. 计算交集字符数
    Set<Character> intersection = new HashSet<>(chars1);
    intersection.retainAll(chars2);
    int commonChars = intersection.size();

    // 3. 计算较短短语的字符数作为基准
    int maxLength = Math.max(str1.length(), str2.length());

    // 4. 判断共同字符占比
    return (double)commonChars / maxLength;
  }

}