package com.jdx.rover.monitor.manager.accident;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.monitor.manager.base.BaseManager;
import com.jdx.rover.monitor.po.Accident;
import com.jdx.rover.monitor.repository.mapper.AccidentMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 事故manager
 */
@Service
public class AccidentManager extends BaseManager<AccidentMapper, Accident> {

    /**
     * 根据事故编号获取事故信息
     * @param accidentNo
     * @return
     */
    public Accident selectByAccidentNo(String accidentNo) {
        return lambdaQuery().eq(Accident::getAccidentNo,accidentNo).one();
    }

    /**
     * 获取当日技术支持未处理事故
     * @param vehicleName
     * @return
     */
    public List<Accident> listTodayWaitAccident(String vehicleName) {
        LambdaQueryWrapper<Accident> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Accident::getVehicleName, vehicleName);
        wrapper.eq(Accident::getIsTechnicalSupportHandle, YesOrNoEnum.NO.getValue());

        LocalDate nowLocalDate = LocalDate.now();
        Date nowDate = Date.from(nowLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        wrapper.ge(Accident::getCreateTime, nowDate);

        wrapper.orderByDesc(Accident::getCreateTime);
        wrapper.last("limit 100");
        List<Accident> accidentList = this.list(wrapper);
        return accidentList;
    }

    /**
     * 根据影子事件编号查询事故信息
     * @param shadowEventId
     * @return
     */
    public Accident selectByShadowEventId(Integer shadowEventId) {
        LambdaQueryWrapper<Accident> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Accident::getShadowEventId, shadowEventId);
        List<Accident> accidentList = this.list(wrapper);
        return CollectionUtil.isEmpty(accidentList) ? null : accidentList.get(0);
    }

    /**
     * 关联事故与工单
     *
     * @param alarmNumber alarmNumber
     * @param issueNumber issueNumber
     */
    public void associateIssueNumber(String alarmNumber, String issueNumber) {
        if (StrUtil.isBlank(alarmNumber) || StrUtil.isBlank(issueNumber)) {
            return;
        }
        lambdaUpdate()
            .isNull(Accident::getIssueNumber)
            .eq(Accident::getAlarmNumber, alarmNumber)
            .set(Accident::getIssueNumber, issueNumber)
            .update();
    }

    /**
     * 根据事故编号查询单个事故信息。
     * @param issueNumber 事故编号。
     * @return 匹配的Accident对象，若不存在则返回null。
     */
    public List<Accident> selectByIssueNumber(String issueNumber) {
        return lambdaQuery().eq(Accident::getIssueNumber,issueNumber).list();
    }
}
