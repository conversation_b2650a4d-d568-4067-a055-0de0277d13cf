/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.websocket;

import cn.hutool.core.util.StrUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.bo.map.MapPointBO;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.client.config.ServletAwareConfigurator;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionTaskManager;
import com.jdx.rover.monitor.manager.notice.JdMeNoticeManager;
import com.jdx.rover.monitor.manager.utils.map.TransformUtility;
import com.jdx.rover.monitor.po.datacollection.DataCollectionTask;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.datacollection.DataCollectionVehicleUserRepository;
import com.jdx.rover.monitor.service.datacollection.MobileDataCollectionService;
import com.jdx.rover.monitor.service.listener.redis.DataCollectionTaskVehicleListener;
import com.jdx.rover.monitor.vo.datacollection.ws.DataCollectionWsVehicleLocation;
import com.jdx.rover.monitor.vo.datacollection.ws.DataCollectionWsVehicleOccupied;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import jakarta.websocket.CloseReason;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 京小鸽数据采集进入采集模式websocket
 *
 * <AUTHOR>
 */
@Slf4j
@ServerEndpoint(value = "/minimonitor/client/ws/mobile/data/collection/collection_mode", configurator = ServletAwareConfigurator.class)
@Component
public class MobileDataCollectionWebSocketServer {

    private static MobileDataCollectionService mobileDataCollectionService;

    private static JdMeNoticeManager jdMeNoticeManager;

    private static VehicleRealtimeRepository vehicleRealtimeRepository;

    private static DataCollectionVehicleUserRepository dataCollectionVehicleUserRepository;

    private static DataCollectionTaskManager dataCollectionTaskManager;

    // 咚咚通知标题
    private static final String DD_NOTICE_TITLE = "数采车WS告警";

   // 咚咚通知人
    private static final String DD_NOTICE_ERP = "linbingbing6,cuihongjian1,gulin21";

    @Autowired
    public void setMobileDataCollectionService(MobileDataCollectionService mobileDataCollectionService) {
        MobileDataCollectionWebSocketServer.mobileDataCollectionService = mobileDataCollectionService;
    }

    @Autowired
    public void setJdMeNoticeManager(JdMeNoticeManager jdMeNoticeManager) {
        MobileDataCollectionWebSocketServer.jdMeNoticeManager = jdMeNoticeManager;
    }

    @Autowired
    public void setVehicleRealtimeRepository(VehicleRealtimeRepository vehicleRealtimeRepository) {
        MobileDataCollectionWebSocketServer.vehicleRealtimeRepository = vehicleRealtimeRepository;
    }

    @Autowired
    public void setDataCollectionVehicleUserRepository(DataCollectionVehicleUserRepository dataCollectionVehicleUserRepository) {
        MobileDataCollectionWebSocketServer.dataCollectionVehicleUserRepository = dataCollectionVehicleUserRepository;
    }

    @Autowired
    public void setDataCollectionTaskManager(DataCollectionTaskManager dataCollectionTaskManager) {
        MobileDataCollectionWebSocketServer.dataCollectionTaskManager = dataCollectionTaskManager;
    }

    /**
     * 记录当前在线连接数
     */
    private static final AtomicInteger onlineCount = new AtomicInteger(0);

    /**
     * Websocket客户端
     */
    private final WebsocketClientBO client = new WebsocketClientBO();

    @OnOpen
    public void onOpen(Session session) {
        log.info("[数采车]进入采集模式建立ws连接开始, sessionId={}", session.getId());
        onlineCount.incrementAndGet();
        Map<String, List<String>> requestParams = session.getRequestParameterMap();
        String userName = requestParams.get("userName").get(0);
        String vehicleName = requestParams.get("vehicleName").get(0);
        if (StringUtils.isBlank(userName) || StringUtils.isBlank(vehicleName)) {
            log.error("[数采车]进入采集模式建立ws连接失败，用户或车号不存在，直接关闭连接，sessionId={}", session.getId());
            tryCloseSession(session);
            return;
        }

        this.client.setUsername(userName);
        this.client.setSessionId(session.getId());
        this.client.setVehicleName(vehicleName);
        this.client.setWsConnectionTime(System.currentTimeMillis());

        boolean validConn = true;
        // 判断车辆是否有采集任务
        DataCollectionTask task = dataCollectionTaskManager.getUnfinishedTaskByVehicleName(vehicleName);
        if (Objects.isNull(task)) {
            validConn = false;
            sendWebsocketData(session, WsResult.success(WebsocketEventTypeEnum.DATA_COLLECTION_FINISHED.getValue()));
        }

        // 判断车辆是否已有用户登录，如果已有用户登录则拦截新用户
        String existingUsername = dataCollectionVehicleUserRepository.get(vehicleName);
        if (StrUtil.isNotBlank(existingUsername)) {
            log.info("[数采车]车辆已有ws连接，车号={}, 原用户={}", vehicleName, existingUsername);
            if (StrUtil.equals(existingUsername, userName)) {
                log.info("[数采车]车辆已有ws连接，原用户与当前用户一致，车号={}, 原用户={}", vehicleName, existingUsername);
            } else {
                validConn = false;
                sendWebsocketData(session, WsResult.success(WebsocketEventTypeEnum.DATA_COLLECTION_OCCUPIED.getValue(),
                        new DataCollectionWsVehicleOccupied(existingUsername)));
            }
        }

        if(validConn) {
            mobileDataCollectionService.updateTaskCreateUser(userName, task);
            dataCollectionVehicleUserRepository.save(vehicleName, userName);
            subscribeVehicleEvent(session);
        }
        log.info("[数采车]{}进入采集模式与用户{}建立ws连接成功，sessionId={}, 在线连接数={}.", vehicleName, userName,
            session.getId(), onlineCount.intValue());
    }

    @OnClose
    public void onClose(Session session, CloseReason reason) {
        log.info("[数采车]关闭ws连接开始，sessionId:{}, closeReason:{}", session.getId(), reason);
        String vehicleName = client.getVehicleName();
        String username = client.getUsername();
        if(StringUtils.isNoneBlank(vehicleName, username)) {
            dataCollectionVehicleUserRepository.remove(vehicleName, username);
        }
        unSubscribeVehicleResponse();
        onlineCount.decrementAndGet();
        log.info("[数采车]关闭ws连接成功，sessionId={}，在线连接数={}.", session.getId(), onlineCount.intValue());
    }

    /**
     * 异常
     *
     * @param session   会话
     * @param throwable 异常
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        String sessionId = client.getSessionId();
        log.error("[数采车]ws连接异常，sessionId={}, 异常:{}", sessionId, throwable);
        tryCloseSession(session);
        log.info("[数采车]ws连接异常，断开连接，sessionId={}，在线连接数={}.", sessionId, onlineCount.intValue());
    }

    /**
     * 尝试关闭会话
     *
     * @param session 会话
     */
    private void tryCloseSession(Session session) {
        try {
            if (session != null && session.isOpen()) {
                session.close();
            }
        } catch (IOException e) {
            log.error("[数采车]关闭ws连接异常，sessionId={} 异常:{}", client.getSessionId(), e);
            jdMeNoticeManager.sendNotice(DD_NOTICE_TITLE,
                String.format("[数采车]ws尝试关闭用户[%s]车辆[%s]的会话[%s]异常", client.getUsername(), client.getVehicleName(),
                    client.getSessionId()), DD_NOTICE_ERP);
        }
    }

    /**
     * 订阅数采车车辆事件
     *
     * @param session 会话
     */
    private void subscribeVehicleEvent(Session session) {
        String topicName = RedisTopicEnum.DATA_COLLECTION_TASK_VEHICLE.getValue() + client.getVehicleName();
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
//        if (rTopic.countSubscribers() > 0) {
//            log.info("mini-monitor webSocket has subscribers session={}, topic={},subscribers={}", session.getId(), topicName, rTopic.countSubscribers());
//            rTopic.removeAllListeners();
//        }
        MessageListener<String> messageListener = new DataCollectionTaskVehicleListener(session, client.getUsername(),
            client.getVehicleName());
        int listenerId = rTopic.addListener(String.class, messageListener);
        log.info("[数采车]ws监听RTopic消息，topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(),
            rTopic.countSubscribers());
        if (this.client.getEventListenerId() == null) {
            this.client.setEventListenerId(listenerId);
        }
        log.info("[数采车]ws订阅采集任务车辆RTopic消息!{}", this.client);
    }

    /**
     * 取消订阅数采车车辆事件
     */
    private void unSubscribeVehicleResponse() {
        if (this.client.getEventListenerId() == null) {
            return;
        }
        String topicName = RedisTopicEnum.DATA_COLLECTION_TASK_VEHICLE.getValue() + client.getVehicleName();
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        rTopic.removeListener(this.client.getEventListenerId());
        log.info("[数采车]ws取消订阅采集任务车辆RTopic消息, session={}, topic={}, listenerId={}", client.getSessionId(), topicName, this.client.getEventListenerId());
    }

    /**
     * 接收webSocket消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        String vehicleName = client.getVehicleName();
        log.info("[数采车]{}收到ws消息，sessionId={}, msg={}", vehicleName, session.getId(), message);
        WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
        try {
            if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.DATA_COLLECTION_VEHICLE_LOCATION.getValue())) {
                sendVehicleLocation(session);
            }
        } catch (Exception e) {
            log.error("[数采车]{}收到ws消息处理异常，sessionId={}, ", vehicleName, session.getId(), e);
            sendWebsocketData(session, WsResult.error(websocketVO.getEventType()));
        }
    }

    /**
     * 发送车辆位置信息
     * @param session 会话
     */
    private void sendVehicleLocation(Session session) {
        String vehicleName = client.getVehicleName();
        VehicleRealtimeInfoDTO vehicleRealtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
        if (Objects.isNull(vehicleRealtimeInfoDTO)) {
            sendWebsocketData(session, WsResult.error(WebsocketEventTypeEnum.DATA_COLLECTION_VEHICLE_LOCATION.getValue()));
            return;
        }
        if (TransformUtility.isOutOfChina(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon())) {
            sendWebsocketData(session, WsResult.error(WebsocketEventTypeEnum.DATA_COLLECTION_VEHICLE_LOCATION.getValue()));
            return;
        }
        MapPointBO mapPointBO = TransformUtility.toGCJ02Point(vehicleRealtimeInfoDTO.getLat(), vehicleRealtimeInfoDTO.getLon());
        if (Objects.isNull(mapPointBO)) {
            sendWebsocketData(session, WsResult.error(WebsocketEventTypeEnum.DATA_COLLECTION_VEHICLE_LOCATION.getValue()));
            return;
        }
        DataCollectionWsVehicleLocation locationMsg = new DataCollectionWsVehicleLocation(mapPointBO.getLongitude(),
            mapPointBO.getLatitude());
        sendWebsocketData(session, WsResult.success(WebsocketEventTypeEnum.DATA_COLLECTION_VEHICLE_LOCATION.getValue(), locationMsg));
    }

    /**
     * 发送ws数据
     * @param session session
     * @param data 数据
     */
    private void sendWebsocketData(Session session, Object data) {
        String jsonStr = JsonUtils.writeValueAsString(data);
        String vehicleName = client.getVehicleName();
        String username = client.getUsername();
        synchronized(session) {
            try {
                session.getBasicRemote().sendText(jsonStr);
                log.info("[数采车]发送采集任务车辆ws消息, vehicleName:{}, username={}, msg={}", vehicleName, username, jsonStr);
            } catch (IOException e) {
                log.error("[数采车]发送采集任务车辆ws消息失败,vehicleName:{}, username={}, msg={}", vehicleName, username, jsonStr, e);
                jdMeNoticeManager.sendNotice(DD_NOTICE_TITLE, String.format("[数采车]ws给用户%s发送车辆%s消息异常", username, vehicleName), DD_NOTICE_ERP);
                throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getMessage());
            }
        }
    }
}
