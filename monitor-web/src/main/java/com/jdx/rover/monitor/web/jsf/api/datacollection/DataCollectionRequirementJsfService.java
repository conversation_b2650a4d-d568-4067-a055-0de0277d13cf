/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.web.jsf.api.datacollection;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionRequirementDetailDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionRequirementDetailScene;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionRequirementPageDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneDetailDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTagDTO;
import com.jdx.rover.monitor.vo.datacollection.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 数据采集需求相关接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface DataCollectionRequirementJsfService {

    /**
     * 新建数据采集需求
     *
     * @param addDataCollectionRequirementVO 新建需求请求参数
     */
    HttpResult<Void> addRequirement(@NotNull(message = "请求参数不能为空") @Valid AddDataCollectionRequirementVO addDataCollectionRequirementVO);

    /**
     * 新建数据采集标签
     *
     * @param addDataCollectionTagVO 新建标签请求参数
     * @return 创建的标签信息
     */
    HttpResult<DataCollectionTagDTO> addTag(@NotNull(message = "请求参数不能为空") @Valid AddDataCollectionTagVO addDataCollectionTagVO);

    /**
     * 分页获取需求列表
     *
     * @param dataCollectionRequirementPageVO 分页查询参数
     * @return 需求分页列表
     */
    HttpResult<PageDTO<DataCollectionRequirementPageDTO>> getRequirementPageList(@NotNull(message = "请求参数不能为空") @Valid DataCollectionRequirementPageVO dataCollectionRequirementPageVO);

    /**
     * 更改需求状态
     *
     * @param dataCollectionRequirementChangeStateVO 更改状态请求参数
     */
    HttpResult<Void> changeRequirementState(@NotNull(message = "请求参数不能为空") @Valid DataCollectionRequirementChangeStateVO dataCollectionRequirementChangeStateVO);

    /**
     * 获取全量标签列表
     *
     * @return 标签列表
     */
    HttpResult<List<DataCollectionTagDTO>> getTagList();

    /**
     * 获取需求详情
     *
     * @param dataCollectionRequirementDetailVO 需求详情查询参数
     * @return 需求详情
     */
    HttpResult<DataCollectionRequirementDetailDTO> getTaskDetail(@NotNull(message = "请求参数不能为空") @Valid DataCollectionRequirementDetailVO dataCollectionRequirementDetailVO);

    /**
     * 分页获取需求关联场景列表
     *
     * @param dataCollectionRequirementScenePageVO dataCollectionRequirementScenePageVO
     * @return DataCollectionRequirementDetailScene
     */
    HttpResult<PageDTO<DataCollectionRequirementDetailScene>> getRequirementScenePageList(DataCollectionRequirementScenePageVO dataCollectionRequirementScenePageVO);

    /**
     * 编辑需求详情
     *
     * @param dataCollectionRequirementEditVO 编辑需求详情请求参数
     */
    HttpResult<Void> editRequirement(@NotNull(message = "请求参数不能为空") @Valid DataCollectionRequirementEditVO dataCollectionRequirementEditVO);

    /**
     * 查看需求关联场景详情
     *
     * @param dataCollectionSceneDetailVO 场景详情查询参数
     * @return 场景详情
     */
    HttpResult<DataCollectionSceneDetailDTO> getSceneDetail(@NotNull(message = "请求参数不能为空") @Valid DataCollectionSceneDetailVO dataCollectionSceneDetailVO);

    /**
     * 模糊查询需求列表
     */
    HttpResult<List<DataCollectionRequirementPageDTO>> getRequirementList(@NotNull(message = "请求参数不能为空") @Valid DataCollectionRequirementListVO requirementListVo);
}