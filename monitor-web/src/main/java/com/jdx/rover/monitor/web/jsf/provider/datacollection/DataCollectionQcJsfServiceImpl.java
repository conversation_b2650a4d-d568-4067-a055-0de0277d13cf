/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.web.jsf.provider.datacollection;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneLinkRequirementDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTaskDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTaskPageDTO;
import com.jdx.rover.monitor.service.datacollection.DataCollectionQcService;
import com.jdx.rover.monitor.vo.datacollection.*;
import com.jdx.rover.monitor.web.jsf.api.datacollection.DataCollectionQcWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数采车质检接口
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Service
@RequiredArgsConstructor
public class DataCollectionQcJsfServiceImpl extends AbstractProvider<DataCollectionQcWebJsfService> implements DataCollectionQcWebJsfService {

    /**
     * 数据采集质检服务
     */
    private final DataCollectionQcService dataCollectionQcService;

    @Override
    @ServiceInfo(name = "[分页查询采集任务]", webUrl = "/monitor/web/data-collection/getPageCollectionTask")
    public HttpResult<PageDTO<DataCollectionTaskPageDTO>> getPageCollectionTask(DataCollectionTaskPageVO dataCollectionTaskPageVo) {
        return JsfResponse.response(() -> dataCollectionQcService.getPageCollectionTask(dataCollectionTaskPageVo));
    }

    @Override
    @ServiceInfo(name = "[查询采集任务详情]", webUrl = "/monitor/web/data-collection/getCollectionTaskDetail")
    public HttpResult<DataCollectionTaskDTO> getCollectionTaskDetail(DataCollectionTaskDetailVO dataCollectionTaskDetailVo) {
        return JsfResponse.response(() -> dataCollectionQcService.getCollectionTaskDetail(dataCollectionTaskDetailVo));
    }

    @Override
    @ServiceInfo(name = "[查询采集任务关联场景详情]", webUrl = "/monitor/web/data-collection/getTaskSceneDetail")
    public HttpResult<DataCollectionSceneDTO> getTaskSceneDetail(DataCollectionSceneVO dataCollectionSceneVo) {
        return JsfResponse.response(() -> dataCollectionQcService.getTaskSceneDetail(dataCollectionSceneVo));
    }

    @Override
    @ServiceInfo(name = "[增加场景关联标签]", webUrl = "/monitor/web/data-collection/addSceneTag")
    public HttpResult<Integer> addSceneTag(DataCollectionSceneAddTagVO dataCollectionSceneAddTagVo) {
        return JsfResponse.response(() -> dataCollectionQcService.addSceneTag(dataCollectionSceneAddTagVo));
    }

    @Override
    @ServiceInfo(name = "[场景关联需求列表]", webUrl = "/monitor/web/data-collection/getMatchedRequirementList")
    public HttpResult<List<DataCollectionSceneLinkRequirementDTO>> getMatchedRequirementList(DataCollectionSceneDetailVO dataCollectionSceneDetailVo) {
        return JsfResponse.response(() -> dataCollectionQcService.getMatchedRequirementList(dataCollectionSceneDetailVo));
    }

    @Override
    @ServiceInfo(name = "[场景关联需求]", webUrl = "/monitor/web/data-collection/associateSceneAndRequirement")
    public HttpResult<Void> associateSceneAndRequirement(DataCollectionSceneLinkRequirementVO sceneLinkRequirementVo) {
        return JsfResponse.response(() -> dataCollectionQcService.associateSceneAndRequirement(sceneLinkRequirementVo));
    }

    @Override
    @ServiceInfo(name = "[完成质检]", webUrl = "/monitor/web/data-collection/completeSceneTask")
    public HttpResult<Void> completeSceneTask(DataCollectionSceneDetailVO sceneDetailVo) {
        return JsfResponse.response(() -> dataCollectionQcService.completeSceneTask(sceneDetailVo));
    }

    @Override
    @ServiceInfo(name = "[删除场景关联标签]", webUrl = "/monitor/web/data-collection/deleteSceneTag")
    public HttpResult<Integer> deleteSceneTag(DataCollectionSceneDeleteTagVO dataCollectionSceneDeleteTagVO) {
        return JsfResponse.response(() -> dataCollectionQcService.deleteSceneTag(dataCollectionSceneDeleteTagVO));
    }

    @Override
    @ServiceInfo(name = "[场景关联需求列表]", webUrl = "/monitor/web/data-collection/addMatchedRequirement")
    public HttpResult<List<DataCollectionSceneLinkRequirementDTO>> addMatchedRequirement(DataCollectionSceneLinkRequirementVO sceneLinkRequirementVo) {
        return JsfResponse.response(() -> dataCollectionQcService.addMatchedRequirement(sceneLinkRequirementVo));
    }
}