/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.web.jsf.provider.datacollection;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionRequirementDetailScene;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTagDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionRequirementPageDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionRequirementDetailDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneDetailDTO;
import com.jdx.rover.monitor.service.datacollection.DataCollectionService;
import com.jdx.rover.monitor.vo.datacollection.*;
import com.jdx.rover.monitor.web.jsf.api.datacollection.DataCollectionRequirementJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据采集需求相关接口实现
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class DataCollectionRequirementJsfServiceImpl extends AbstractProvider<DataCollectionRequirementJsfService> implements DataCollectionRequirementJsfService {

    /**
     * 数据采集服务
     */
    private final DataCollectionService dataCollectionService;

    @Override
    @ServiceInfo(name = "[新建数据采集需求]", webUrl = "/monitor/web/data-collection/addRequirement")
    public HttpResult<Void> addRequirement(AddDataCollectionRequirementVO addDataCollectionRequirementVO) {
        return JsfResponse.response(() -> dataCollectionService.addRequirement(addDataCollectionRequirementVO, UserUtils.getLoginUser()));
    }

    @Override
    @ServiceInfo(name = "[新建数据采集标签]", webUrl = "/monitor/web/data-collection/addTag")
    public HttpResult<DataCollectionTagDTO> addTag(AddDataCollectionTagVO addDataCollectionTagVO) {
        return JsfResponse.response(() -> dataCollectionService.addTag(addDataCollectionTagVO, UserUtils.getLoginUser()));
    }

    @Override
    @ServiceInfo(name = "[分页获取需求列表]", webUrl = "/monitor/web/data-collection/getRequirementPageList")
    public HttpResult<PageDTO<DataCollectionRequirementPageDTO>> getRequirementPageList(DataCollectionRequirementPageVO dataCollectionRequirementPageVO) {
        return JsfResponse.response(() -> dataCollectionService.getRequirementPageList(dataCollectionRequirementPageVO));
    }

    @Override
    @ServiceInfo(name = "[更改需求状态]", webUrl = "/monitor/web/data-collection/changeRequirementState")
    public HttpResult<Void> changeRequirementState(DataCollectionRequirementChangeStateVO dataCollectionRequirementChangeStateVO) {
        return JsfResponse.response(() -> dataCollectionService.changeRequirementState(dataCollectionRequirementChangeStateVO, UserUtils.getLoginUser()));
    }

    @Override
    @ServiceInfo(name = "[获取全量标签列表]", webUrl = "/monitor/web/data-collection/getTagList")
    public HttpResult<List<DataCollectionTagDTO>> getTagList() {
        return JsfResponse.response(dataCollectionService::getTagList);
    }

    @Override
    @ServiceInfo(name = "[获取需求详情]", webUrl = "/monitor/web/data-collection/getTaskDetail")
    public HttpResult<DataCollectionRequirementDetailDTO> getTaskDetail(DataCollectionRequirementDetailVO dataCollectionRequirementDetailVO) {
        return JsfResponse.response(() -> dataCollectionService.getTaskDetail(dataCollectionRequirementDetailVO));
    }

    @Override
    @ServiceInfo(name = "[分页获取需求关联场景列表]", webUrl = "/monitor/web/data-collection/getRequirementScenePageList")
    public HttpResult<PageDTO<DataCollectionRequirementDetailScene>> getRequirementScenePageList(DataCollectionRequirementScenePageVO dataCollectionRequirementScenePageVO) {
        return JsfResponse.response(() -> dataCollectionService.getRequirementScenePageList(dataCollectionRequirementScenePageVO));
    }

    @Override
    @ServiceInfo(name = "[编辑需求详情]", webUrl = "/monitor/web/data-collection/editRequirement")
    public HttpResult<Void> editRequirement(DataCollectionRequirementEditVO dataCollectionRequirementEditVO) {
        return JsfResponse.response(() -> dataCollectionService.editRequirement(dataCollectionRequirementEditVO, UserUtils.getLoginUser()));
    }

    @Override
    @ServiceInfo(name = "[查看需求关联场景详情]", webUrl = "/monitor/web/data-collection/getSceneDetail")
    public HttpResult<DataCollectionSceneDetailDTO> getSceneDetail(DataCollectionSceneDetailVO dataCollectionSceneDetailVO) {
        return JsfResponse.response(() -> dataCollectionService.getSceneDetail(dataCollectionSceneDetailVO));
    }

    @Override
    @ServiceInfo(name = "[模糊查询需求列表]", webUrl = "/monitor/web/data-collection/getRequirementList")
    public HttpResult<List<DataCollectionRequirementPageDTO>> getRequirementList(DataCollectionRequirementListVO requirementListVo) {
        return JsfResponse.response(() -> dataCollectionService.getRequirementList(requirementListVo));
    }
}