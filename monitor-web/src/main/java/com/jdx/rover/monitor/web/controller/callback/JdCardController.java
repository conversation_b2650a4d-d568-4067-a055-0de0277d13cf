package com.jdx.rover.monitor.web.controller.callback;

import com.jdx.rover.monitor.dto.callback.JdCardCallbackDTO;
import com.jdx.rover.monitor.service.callback.JdCardService;
import com.jdx.rover.monitor.vo.callback.JdCardCallbackVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 京ME消息卡片
 */
@RequiredArgsConstructor
@RequestMapping(value = "/monitor/web/jdcard")
@RestController
public class JdCardController {

    private final JdCardService jdCardService;

    /**
     * 处理京东卡回调请求
     * @param jdCardCallbackVO 回调数据对象
     * @return HttpResult 对象，包含回调处理结果
     */
    @PostMapping("/callback")
    public JdCardCallbackDTO callback(@RequestBody JdCardCallbackVO jdCardCallbackVO) {
        return jdCardService.callback(jdCardCallbackVO);
    }
}
