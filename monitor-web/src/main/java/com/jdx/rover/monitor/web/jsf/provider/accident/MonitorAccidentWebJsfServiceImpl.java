/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.web.jsf.provider.accident;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.accident.AccidentBasicInfoDTO;
import com.jdx.rover.monitor.dto.accident.AccidentTagDTO;
import com.jdx.rover.monitor.dto.accident.GetAccidentPageListDTO;
import com.jdx.rover.monitor.dto.accident.MonitorAccidentDetailDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.web.MonitorAccidentService;
import com.jdx.rover.monitor.vo.accident.*;
import com.jdx.rover.monitor.web.jsf.api.accident.MonitorAccidentWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.List;

/**
 * 事故看板jsf接口
 */
@Service
@RequiredArgsConstructor
public class MonitorAccidentWebJsfServiceImpl extends AbstractProvider<MonitorAccidentWebJsfService> implements MonitorAccidentWebJsfService {

    /**
     * 监控事故服务对象，用于调用事故相关的业务逻辑。
     */
    private final MonitorAccidentService monitorAccidentService;

    @ServiceInfo(name = "分页获取事故列表", webUrl = "/monitor/web/accident/get_accident_page_list")
    public HttpResult<PageDTO<GetAccidentPageListDTO>> getAccidentPageList(@Valid GetAccidentPageListVO getAccidentPageListVO) {
        return HttpResult.success(monitorAccidentService.getAccidentPageList(getAccidentPageListVO));
    }

    @ServiceInfo(name = "获取事故基础信息", webUrl = "/monitor/web/accident/get_accident_basic_info")
    public HttpResult<AccidentBasicInfoDTO> getAccidentBasicInfo(@Valid GetAccidentDetailVO getAccidentDetailVo) {
        return HttpResult.success(monitorAccidentService.getAccidentBasicInfo(getAccidentDetailVo.getAccidentNo()));
    }

    @ServiceInfo(name = "获取事故详情", webUrl = "/monitor/web/accident/get_accident_detail")
    public HttpResult<MonitorAccidentDetailDTO> getAccidentDetail(@Valid GetAccidentDetailVO getAccidentDetailVo) {
        return HttpResult.success(monitorAccidentService.getAccidentDetail(getAccidentDetailVo.getAccidentNo()));
    }

    @ServiceInfo(name = "技术支持编辑事故", webUrl = "/monitor/web/accident/technical_support_edit_accident")
    public HttpResult<String> technicalSupportEditAccident(@Valid TechnicalSupportEditAccidentVO technicalSupportEditAccidentVO) {
        MonitorErrorEnum monitorErrorEnum = monitorAccidentService.technicalSupportEditAccident(technicalSupportEditAccidentVO);
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "安全组编辑事故", webUrl = "/monitor/web/accident/safety_group_edit_accident")
    public HttpResult<String> safetyGroupEditAccident(@Valid SafetyGroupEditAccidentVO safetyGroupEditAccidentVO) {
        MonitorErrorEnum monitorErrorEnum = monitorAccidentService.safetyGroupEditAccident(safetyGroupEditAccidentVO);
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "手动创建事故", webUrl = "/monitor/web/accident/manual_create_accident")
    public HttpResult<String> manualCreateAccident(@Valid ManualCreateAccidentVO manualCreateAccidentVO) {
        MonitorErrorEnum monitorErrorEnum = monitorAccidentService.manualCreateAccident(manualCreateAccidentVO);
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "获取事故标签&模块", webUrl = "/monitor/web/accident/get_tag_list")
    public HttpResult<List<AccidentTagDTO>> getTagList() {
        return HttpResult.success(monitorAccidentService.getTagList());
    }

    @Override
    public HttpResult<String> refreshHistory() {
        monitorAccidentService.refreshHistory();
        return new HttpResult(MonitorErrorEnum.OK.getCode(), MonitorErrorEnum.OK.getMessage(), null);
    }

    @Override
    public HttpResult<String> initJDAccidentData(InitJDAccidentVO initJDAccidentVO) {
        monitorAccidentService.initJDAccidentData(initJDAccidentVO);
        return new HttpResult(MonitorErrorEnum.OK.getCode(), MonitorErrorEnum.OK.getMessage(), null);
    }

    @Override
    public HttpResult<String> manualUpdateAccidentBugStatus() {
        monitorAccidentService.updateBugStatus();
        return new HttpResult(MonitorErrorEnum.OK.getCode(), MonitorErrorEnum.OK.getMessage(), null);
    }

    @Override
    public HttpResult<String> initNeolixAccidentData(InitNeolixAccidentVO initNeolixAccidentVO) {
        monitorAccidentService.initNeolixAccidentData(initNeolixAccidentVO);
        return new HttpResult(MonitorErrorEnum.OK.getCode(), MonitorErrorEnum.OK.getMessage(), null);
    }
}
