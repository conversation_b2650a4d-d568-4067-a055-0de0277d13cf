/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.web.jsf.api.datacollection;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionSceneLinkRequirementDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTaskDTO;
import com.jdx.rover.monitor.dto.datacollection.DataCollectionTaskPageDTO;
import com.jdx.rover.monitor.vo.datacollection.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 数采车接口
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
public interface DataCollectionQcWebJsfService {

    /**
     * 分页获取数据采集任务列表
     * @param dataCollectionTaskPageVo 包含分页参数和筛选条件的查询对象
     * @return
     */
    HttpResult<PageDTO<DataCollectionTaskPageDTO>> getPageCollectionTask(DataCollectionTaskPageVO dataCollectionTaskPageVo);

    /**
     * 获取数据采集任务的详细信息
     * @param dataCollectionTaskDetailVo 数据采集任务详情视图对象，包含查询任务详情所需的参数
     * @return HttpResult<DataCollectionTaskDTO> 包含数据采集任务DTO的HTTP响应结果
     */
    HttpResult<DataCollectionTaskDTO> getCollectionTaskDetail(DataCollectionTaskDetailVO dataCollectionTaskDetailVo);

    /**
     * 获取场景详情
     *
     * @param dataCollectionSceneVo
     * @return DataCollectionSceneDTO
     */
    HttpResult<DataCollectionSceneDTO> getTaskSceneDetail(@NotNull(message = "请求参数不能为空") @Valid DataCollectionSceneVO dataCollectionSceneVo);

    /**
     * 新增场景关联标签
     *
     * @param dataCollectionSceneAddTagVo
     * @return
     */
    HttpResult<Integer> addSceneTag(@NotNull(message = "请求参数不能为空") @Valid DataCollectionSceneAddTagVO dataCollectionSceneAddTagVo);

    /**
     * 获取匹配的需求列表
     */
    HttpResult<List<DataCollectionSceneLinkRequirementDTO>> getMatchedRequirementList(@NotNull(message = "请求参数不能为空") @Valid DataCollectionSceneDetailVO dataCollectionSceneDetailVo);

    /**
     * 关联场景和需求
     */
    HttpResult<Void> associateSceneAndRequirement(@NotNull(message = "请求参数不能为空") @Valid DataCollectionSceneLinkRequirementVO sceneLinkRequirementVo);

    /**
     * 完成质检
     */
    HttpResult<Void> completeSceneTask(@NotNull(message = "请求参数不能为空") @Valid DataCollectionSceneDetailVO sceneDetailVo);

    /**
     * 删除场景标签
     */
    HttpResult<Integer> deleteSceneTag(@NotNull(message = "请求参数不能为空") @Valid DataCollectionSceneDeleteTagVO dataCollectionSceneDeleteTagVO);

    /**
     * 新增匹配的需求信息
     */
    public HttpResult<List<DataCollectionSceneLinkRequirementDTO>> addMatchedRequirement(DataCollectionSceneLinkRequirementVO sceneLinkRequirementVo);

}