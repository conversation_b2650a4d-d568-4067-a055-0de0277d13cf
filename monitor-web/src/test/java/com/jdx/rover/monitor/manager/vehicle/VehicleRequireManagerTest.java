package com.jdx.rover.monitor.manager.vehicle;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.monitor.repository.redis.metadata.VehicleRequireRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class VehicleRequireManagerTest {

    @Autowired
    private VehicleRequireManager vehicleRequireManager;

    @Autowired
    private VehicleRequireRepository vehicleRequireRepository;

    @Test
    public void test_redis(){

       vehicleRequireRepository.setVehicleIsUnderRequire("JDX11", true,600L);
    }
}
