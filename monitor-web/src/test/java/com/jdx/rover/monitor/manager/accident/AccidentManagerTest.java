package com.jdx.rover.monitor.manager.accident;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailBO;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailQueryBO;
import com.jdx.rover.monitor.dto.jdme.JueActionInfo;
import com.jdx.rover.monitor.dto.jdme.JueCardData;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButton;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonHref;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtonText;
import com.jdx.rover.monitor.dto.jdme.JueCardDataButtons;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementText;
import com.jdx.rover.monitor.dto.jdme.JueCardDataElementTextItem;
import com.jdx.rover.monitor.dto.jdme.JueCardDataEnums;
import com.jdx.rover.monitor.dto.jdme.JueCardDataHeader;
import com.jdx.rover.monitor.dto.jdme.JueCardDataHeaderTitle;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import com.jdx.rover.monitor.manager.jdme.AccidentJdmePushManager;
import com.jdx.rover.monitor.service.web.MonitorAccidentService;
import com.jdx.rover.monitor.vo.accident.InitJDAccidentVO;
import com.jdx.rover.monitor.vo.accident.InitNeolixAccidentVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

@ActiveProfiles("test")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class AccidentManagerTest {

    private final AccidentManager accidentManager;

    @Autowired
    private AccidentJdmePushManager accidentJdmePushManager;

    @Autowired
    private MonitorAccidentService monitorAccidentService;


    @Test
    public void test_initJDAccidentData() {
        InitJDAccidentVO initJDAccidentVO = new InitJDAccidentVO();
        initJDAccidentVO.setFileKey("1756298320001-accident-test.xlsx");
        monitorAccidentService.initJDAccidentData(initJDAccidentVO);
    }

    @Test
    public void test_initNeolixAccidentData() {
        InitNeolixAccidentVO initNeolixAccidentVO = new InitNeolixAccidentVO();
        initNeolixAccidentVO.setFileKey("1756454089492-test_neolix_accident.xlsx");
        monitorAccidentService.initNeolixAccidentData(initNeolixAccidentVO);
    }

    @Test
    public void test_getAccidentBoardDetailList() {
        AccidentBoardDetailQueryBO accidentBoardDetailQueryBO = new AccidentBoardDetailQueryBO();
        accidentBoardDetailQueryBO.setAccidentLevel(PreliminaryAccidentLevelEnum.HIGH_RISK.getValue());
        List<AccidentBoardDetailBO> accidentBoardDetailList = accidentManager.getBaseMapper().getAccidentBoardDetailList(accidentBoardDetailQueryBO);
        System.out.println(JsonUtils.writeValueAsString(accidentBoardDetailList));
    }

    @Test
    public void testJdmeCard(){
        JueCardData jueCardData = new JueCardData();
        jueCardData.setGroupId("10218681991");
        List<JueCardDataElementText> elementTextList = new ArrayList<>();

        JueCardDataHeaderTitle headerTitle = new JueCardDataHeaderTitle();
        headerTitle.setContent("测试卡片");

        JueCardDataHeader header = new JueCardDataHeader();
        header.setTheme(JueCardDataEnums.LabelColorType.RED.getCode());
        header.setTitle(headerTitle);
        jueCardData.setHeader(header);

        //问题描述
        JueCardDataElementText elementText = new JueCardDataElementText();
        elementText.setContent(new JueCardDataElementTextItem("问题描述", "sfsfsfsfs", "", JueCardDataEnums.ElementValueType.TEXT));
        elementTextList.add(elementText);
        jueCardData.setElements(elementTextList);

        List<JueCardDataButton> buttonList = new ArrayList<>();

        JueCardDataElementTextItem manualButtonTextItem = new JueCardDataElementTextItem();
        manualButtonTextItem.setLabel("咨询人工");

        JueCardDataButtonText manualButtonText = new JueCardDataButtonText();
        manualButtonText.setContent(manualButtonTextItem);

        JueCardDataButtonHref manualButtonHref = new JueCardDataButtonHref();
        manualButtonHref.setPc("timline://chat/?topin=" + "douyanghui");
        manualButtonHref.setMobile("jdme://jm/biz/im/contact/details?mparam={\"erp\":\"" + "douyanghui" + "\"}");

        JueCardDataButton manualButton = new JueCardDataButton();
        manualButton.setType(JueCardDataEnums.ButtonType.DEFAULT.getCode());
        manualButton.setEnable(true);
        manualButton.setText(manualButtonText);
        manualButton.setHref(manualButtonHref);

        JueCardDataElementTextItem recoverButtonTextItem = new JueCardDataElementTextItem();
        recoverButtonTextItem.setLabel("恢复运营");
        JueCardDataButtonText recoverButtonText = new JueCardDataButtonText();
        recoverButtonText.setContent(recoverButtonTextItem);
        JueActionInfo recoverActionInfo = new JueActionInfo();
        recoverActionInfo.setBusinessType("accident");
        recoverActionInfo.setOperatorType("recover");
        recoverActionInfo.setNumber("112233");
        JueCardDataButton recoverButton = new JueCardDataButton();
        recoverButton.setType(JueCardDataEnums.ButtonType.PRIMARY.getCode());
        recoverButton.setEnable(true);
        recoverButton.setText(recoverButtonText);
        recoverButton.setAction(recoverActionInfo);
        buttonList.add(manualButton);
        buttonList.add(recoverButton);

        JueCardDataButtons buttons = new JueCardDataButtons();
        buttons.setLayout(JueCardDataEnums.ButtonLayout.ROW);
        buttons.setButtons(buttonList);
        jueCardData.setButtons(buttons);

        try {
            accidentJdmePushManager.sendJUEMsg(jueCardData);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
