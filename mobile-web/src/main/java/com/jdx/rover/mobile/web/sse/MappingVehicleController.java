/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.sse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdx.rover.common.utils.result.HttpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/8 19:58
 * @description 车辆映射SSE接口Demo - 用于前端测试SSE连接和数据推送
 */
@Slf4j
@RestController
@RequestMapping("/mobile/applet/sse")
public class MappingVehicleController {

    private static final long SSE_TIMEOUT = 30 * 60 * 1000L; // 30分钟超时
    private static final int HEARTBEAT_INTERVAL = 30; // 30秒心跳间隔
    private static final int DATA_PUSH_INTERVAL = 5; // 5秒数据推送间隔

    // 存储所有活跃的SSE连接
    private final Map<String, SseEmitter> sseEmitters = new ConcurrentHashMap<>();

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    // JSON序列化工具
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 建立SSE连接
     *
     * @param clientId 客户端ID，用于标识不同的连接
     * @return SseEmitter SSE发射器
     */
    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@RequestParam(value = "clientId", required = false) String clientId) {
        // 如果没有提供clientId，生成一个随机ID
        if (clientId == null || clientId.trim().isEmpty()) {
            clientId = "client_" + System.currentTimeMillis() + "_" + new Random().nextInt(1000);
        }

        log.info("SSE连接建立，客户端ID: {}", clientId);

        // 创建SSE发射器，设置超时时间
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

        // 存储连接
        sseEmitters.put(clientId, emitter);

        final String finalClientId = clientId;

        // 设置连接完成回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成，客户端ID: {}", finalClientId);
            sseEmitters.remove(finalClientId);
        });

        // 设置连接超时回调
        emitter.onTimeout(() -> {
            log.info("SSE连接超时，客户端ID: {}", finalClientId);
            sseEmitters.remove(finalClientId);
        });

        // 设置连接错误回调
        emitter.onError((throwable) -> {
            log.error("SSE连接错误，客户端ID: {}, 错误信息: {}", finalClientId, throwable.getMessage());
            sseEmitters.remove(finalClientId);
        });

        try {
            // 发送连接成功消息
            Map<String, Object> connectMsg = new HashMap<>();
            connectMsg.put("type", "connect");
            connectMsg.put("clientId", finalClientId);
            connectMsg.put("message", "SSE连接建立成功");
            connectMsg.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            emitter.send(SseEmitter.event()
                    .name("connect")
                    .data(objectMapper.writeValueAsString(connectMsg)));

            // 启动心跳和数据推送任务
            startHeartbeat(finalClientId);
            startDataPush(finalClientId);

        } catch (IOException e) {
            log.error("发送SSE连接成功消息失败，客户端ID: {}", finalClientId, e);
            sseEmitters.remove(finalClientId);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 手动断开SSE连接
     *
     * @param clientId 客户端ID
     * @return HttpResult 操作结果
     */
    @PostMapping("/disconnect")
    public HttpResult<String> disconnect(@RequestParam("clientId") String clientId) {
        SseEmitter emitter = sseEmitters.get(clientId);
        if (emitter != null) {
            try {
                // 发送断开连接消息
                Map<String, Object> disconnectMsg = new HashMap<>();
                disconnectMsg.put("type", "disconnect");
                disconnectMsg.put("message", "服务端主动断开连接");
                disconnectMsg.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

                emitter.send(SseEmitter.event()
                        .name("disconnect")
                        .data(objectMapper.writeValueAsString(disconnectMsg)));

                emitter.complete();
                sseEmitters.remove(clientId);

                log.info("手动断开SSE连接成功，客户端ID: {}", clientId);
                return HttpResult.success("连接断开成功");
            } catch (IOException e) {
                log.error("手动断开SSE连接失败，客户端ID: {}", clientId, e);
                sseEmitters.remove(clientId);
                return HttpResult.error("连接断开失败: " + e.getMessage());
            }
        } else {
            return HttpResult.error("连接不存在或已断开");
        }
    }

    /**
     * 获取当前活跃连接数
     *
     * @return HttpResult 连接信息
     */
    @GetMapping("/status")
    public HttpResult<Map<String, Object>> getConnectionStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("activeConnections", sseEmitters.size());
        status.put("clientIds", new ArrayList<>(sseEmitters.keySet()));
        status.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        return HttpResult.success(status);
    }

    /**
     * 向指定客户端发送自定义消息
     *
     * @param clientId 客户端ID
     * @param message 消息内容
     * @return HttpResult 发送结果
     */
    @PostMapping("/send")
    public HttpResult<String> sendMessage(@RequestParam("clientId") String clientId,
                                         @RequestParam("message") String message) {
        SseEmitter emitter = sseEmitters.get(clientId);
        if (emitter != null) {
            try {
                Map<String, Object> customMsg = new HashMap<>();
                customMsg.put("type", "custom");
                customMsg.put("message", message);
                customMsg.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

                emitter.send(SseEmitter.event()
                        .name("custom")
                        .data(objectMapper.writeValueAsString(customMsg)));

                log.info("向客户端 {} 发送自定义消息成功: {}", clientId, message);
                return HttpResult.success("消息发送成功");
            } catch (IOException e) {
                log.error("向客户端 {} 发送自定义消息失败: {}", clientId, message, e);
                sseEmitters.remove(clientId);
                return HttpResult.error("消息发送失败: " + e.getMessage());
            }
        } else {
            return HttpResult.error("客户端连接不存在");
        }
    }

    /**
     * 向所有客户端广播消息
     *
     * @param message 广播消息内容
     * @return HttpResult 广播结果
     */
    @PostMapping("/broadcast")
    public HttpResult<String> broadcastMessage(@RequestParam("message") String message) {
        if (sseEmitters.isEmpty()) {
            return HttpResult.error("没有活跃的连接");
        }

        Map<String, Object> broadcastMsg = new HashMap<>();
        broadcastMsg.put("type", "broadcast");
        broadcastMsg.put("message", message);
        broadcastMsg.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        int successCount = 0;
        int failCount = 0;

        Iterator<Map.Entry<String, SseEmitter>> iterator = sseEmitters.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, SseEmitter> entry = iterator.next();
            String clientId = entry.getKey();
            SseEmitter emitter = entry.getValue();

            try {
                emitter.send(SseEmitter.event()
                        .name("broadcast")
                        .data(objectMapper.writeValueAsString(broadcastMsg)));
                successCount++;
            } catch (IOException e) {
                log.error("向客户端 {} 广播消息失败: {}", clientId, message, e);
                iterator.remove();
                failCount++;
            }
        }

        String result = String.format("广播完成，成功: %d, 失败: %d", successCount, failCount);
        log.info("广播消息: {}, 结果: {}", message, result);
        return HttpResult.success(result);
    }

    /**
     * 启动心跳任务
     *
     * @param clientId 客户端ID
     */
    private void startHeartbeat(String clientId) {
        scheduler.scheduleAtFixedRate(() -> {
            SseEmitter emitter = sseEmitters.get(clientId);
            if (emitter != null) {
                try {
                    Map<String, Object> heartbeat = new HashMap<>();
                    heartbeat.put("type", "heartbeat");
                    heartbeat.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

                    emitter.send(SseEmitter.event()
                            .name("heartbeat")
                            .data(objectMapper.writeValueAsString(heartbeat)));

                    log.debug("发送心跳到客户端: {}", clientId);
                } catch (IOException e) {
                    log.error("发送心跳失败，客户端ID: {}", clientId, e);
                    sseEmitters.remove(clientId);
                }
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
    }

    /**
     * 启动数据推送任务
     *
     * @param clientId 客户端ID
     */
    private void startDataPush(String clientId) {
        scheduler.scheduleAtFixedRate(() -> {
            SseEmitter emitter = sseEmitters.get(clientId);
            if (emitter != null) {
                try {
                    // 生成模拟的车辆映射数据
                    Map<String, Object> vehicleData = generateMockVehicleData();

                    emitter.send(SseEmitter.event()
                            .name("vehicleData")
                            .data(objectMapper.writeValueAsString(vehicleData)));

                    log.debug("推送车辆数据到客户端: {}", clientId);
                } catch (IOException e) {
                    log.error("推送车辆数据失败，客户端ID: {}", clientId, e);
                    sseEmitters.remove(clientId);
                }
            }
        }, DATA_PUSH_INTERVAL, DATA_PUSH_INTERVAL, TimeUnit.SECONDS);
    }

    /**
     * 生成模拟的车辆映射数据
     *
     * @return Map 车辆数据
     */
    private Map<String, Object> generateMockVehicleData() {
        Random random = new Random();

        Map<String, Object> data = new HashMap<>();
        data.put("type", "vehicleMapping");
        data.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // 模拟车辆列表
        List<Map<String, Object>> vehicles = new ArrayList<>();
        String[] vehicleNames = {"V001", "V002", "V003", "V004", "V005"};
        String[] statuses = {"运行中", "空闲", "充电中", "维护中", "离线"};

        for (int i = 0; i < 3; i++) {
            Map<String, Object> vehicle = new HashMap<>();
            vehicle.put("vehicleName", vehicleNames[random.nextInt(vehicleNames.length)]);
            vehicle.put("status", statuses[random.nextInt(statuses.length)]);
            vehicle.put("latitude", 39.9042 + (random.nextDouble() - 0.5) * 0.1);
            vehicle.put("longitude", 116.4074 + (random.nextDouble() - 0.5) * 0.1);
            vehicle.put("speed", random.nextInt(60));
            vehicle.put("battery", random.nextInt(100));
            vehicle.put("lastUpdateTime", LocalDateTime.now().minusSeconds(random.nextInt(300))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            vehicles.add(vehicle);
        }

        data.put("vehicles", vehicles);
        data.put("totalCount", vehicles.size());

        // 模拟统计信息
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("onlineCount", random.nextInt(10) + 1);
        statistics.put("offlineCount", random.nextInt(5));
        statistics.put("chargingCount", random.nextInt(3));
        statistics.put("maintenanceCount", random.nextInt(2));

        data.put("statistics", statistics);

        return data;
    }
}
