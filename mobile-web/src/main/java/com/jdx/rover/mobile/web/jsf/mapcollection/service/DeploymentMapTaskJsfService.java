/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.deployment.DeployEnumDTO;
import com.jdx.rover.monitor.dto.deployment.DeployTaskInfoDTO;
import com.jdx.rover.monitor.dto.deployment.DeployTaskListDTO;
import com.jdx.rover.monitor.dto.deployment.FuzzySearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.MapCollectionTaskExportResultDTO;
import com.jdx.rover.monitor.vo.deployment.*;
import com.jdx.rover.monitor.vo.mapcollection.MapCollectionTaskExportVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 16:27
 * @description 部署平台线路勘查
 */
public interface DeploymentMapTaskJsfService {

    /**
     * 获取部署枚举列表
     */
    HttpResult<List<DeployEnumDTO>> getEnumMapping();

    /**
     * 元素列表模糊搜索
     */
    HttpResult<List<FuzzySearchDTO>> elementFuzzySearch(@NotNull(message = "非法请求") @Valid FuzzySearchVO fuzzySearchVO);

    /**
     * 获取部署地图元素列表
     */
    HttpResult<DeployTaskListDTO> getTaskRouteList(@NotNull(message = "非法请求") @Valid DeployTaskListVO deployTaskListVO);

    /**
     * 获取勘查任务详情
     */
    HttpResult<DeployTaskInfoDTO> getTaskDetail(@NotNull(message = "非法请求") @Valid TaskBaseVO taskBaseVO);

    /**
     * 创建线路规划
     */
    HttpResult<Void> createTaskRoute(@NotNull(message = "非法请求") @Valid DeployTaskCreateVO deployTaskCreateVO);

    /**
     * 编辑线路规划
     */
    HttpResult<Void> modifyTaskRoute(@NotNull(message = "非法请求") @Valid DeployTaskEditVO deployTaskEditVO);

    /**
     * 删除任务
     */
    HttpResult<Void> deleteTask(@NotNull(message = "非法请求") @Valid DeployTaskDeleteVO deployTaskDeleteVO);

    /**
     * 回退任务
     */
    HttpResult<Void> revokeTask(@NotNull(message = "非法请求") @Valid TaskBaseVO taskBaseVO);

    /**
     * 设置勘查任务状态（OPS）
     */
    HttpResult<Void> refreshTaskStatus(List<Integer> taskIds, String taskStatus);

    /**
     * 导出勘查任务geojson
     */
    HttpResult<MapCollectionTaskExportResultDTO> exportMapCollectionTaskData(@NotNull(message = "导出参数不能为空") @Valid MapCollectionTaskExportVO exportVO);

    /**
     * 同步线路任务到部署平台
     */
    HttpResult<Void> syncMapCollectionTask(@NotNull(message = "非法请求") @Valid DeploySyncTaskVO deploySyncTaskVo);
}