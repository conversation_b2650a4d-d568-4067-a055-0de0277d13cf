/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.mobile.web.jsf.datacollection;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.datacollection.service.MobileDataCollRequirementJsfService;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementDetailDTO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementListDTO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementTagsListDTO;
import com.jdx.rover.monitor.service.datacollection.MobileDataCollectionService;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollCanEnterCollectionModeVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollRequirementDetailVO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollTaskGetPlanRouteDTO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskGetPlanRouteVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskRemovePlanRouteVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskSavePlanRouteVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskSceneAudioAssociateVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小程序数据采集需求jsf服务接口实现
 * <AUTHOR>
 * @date 2025/07/21
 */
@Service
@RequiredArgsConstructor
public class MobileDataCollRequirementJsfServiceImpl extends AbstractProvider<MobileDataCollRequirementJsfService> implements MobileDataCollRequirementJsfService {

    private final MobileDataCollectionService mobileDataCollectionService;

    @Override
    @ServiceInfo(name = "[数采车]获取进行中的采集需求列表", webUrl = "/mobile/applet/data-collection/getRequirementList")
    public HttpResult<List<MobileDataCollRequirementListDTO>> getRequirementList() {
        return JsfResponse.response(mobileDataCollectionService::getRequirementList);
    }

    @Override
    @ServiceInfo(name = "[数采车]获取采集需求详情", webUrl = "/mobile/applet/data-collection/getRequirementDetail")
    public HttpResult<MobileDataCollRequirementDetailDTO> getRequirementDetail(MobileDataCollRequirementDetailVO mobileDataCollRequirementDetailVO) {
        return JsfResponse.response(() -> mobileDataCollectionService.getRequirementDetail(mobileDataCollRequirementDetailVO));
    }

    @Override
    @ServiceInfo(name = "[数采车]获取全量进行中的需求标签", webUrl = "/mobile/applet/data-collection/getTags")
    public HttpResult<MobileDataCollRequirementTagsListDTO> getRequirementTagsList() {
        return JsfResponse.response(mobileDataCollectionService::getRequirementTagsList);
    }

    @Override
    @ServiceInfo(name = "[数采车]关联子任务语音信息", webUrl = "/mobile/applet/data-collection/task/associate")
    public HttpResult<Void> associateTaskSceneAudio(MobileDataCollTaskSceneAudioAssociateVO mobileDataCollTaskSceneAudioAssociateVO) {
        return JsfResponse.response(() -> mobileDataCollectionService.associateTaskSceneAudio(mobileDataCollTaskSceneAudioAssociateVO));
    }

    @Override
    @ServiceInfo(name = "[数采车]添加规划路径", webUrl = "/mobile/applet/data-collection/task/savePlanRoute")
    public HttpResult<Void> saveTaskPlanRoute(MobileDataCollTaskSavePlanRouteVO mobileDataCollTaskSavePlanRouteVO) {
        return JsfResponse.response(() -> mobileDataCollectionService.saveTaskPlanRoute(mobileDataCollTaskSavePlanRouteVO));
    }

    @Override
    @ServiceInfo(name = "[数采车]获取规划路径", webUrl = "/mobile/applet/data-collection/task/getPlanRoute")
    public HttpResult<List<MobileDataCollTaskGetPlanRouteDTO>> getTaskPlanRoute(MobileDataCollTaskGetPlanRouteVO mobileDataCollTaskGetPlanRouteVO) {
        return JsfResponse.response(() -> mobileDataCollectionService.getTaskPlanRoute(mobileDataCollTaskGetPlanRouteVO));
    }

    @Override
    @ServiceInfo(name = "[数采车]删除规划路径", webUrl = "/mobile/applet/data-collection/task/removePlanRoute")
    public HttpResult<Void> removeTaskPlanRoute(MobileDataCollTaskRemovePlanRouteVO mobileDataCollTaskRemovePlanRouteVO) {
        return JsfResponse.response(() -> mobileDataCollectionService.removeTaskPlanRoute(mobileDataCollTaskRemovePlanRouteVO));
    }

    @Override
    @ServiceInfo(name = "[数采车]判断是否能进入采集模式", webUrl = "/mobile/applet/data-collection/canEnterCollectionMode")
    public HttpResult<Void> canEnterCollectionMode(MobileDataCollCanEnterCollectionModeVO mobileDataCollCanEnterCollectionModeVO) {
        return JsfResponse.response(() -> mobileDataCollectionService.canEnterCollectionMode(mobileDataCollCanEnterCollectionModeVO));
    }
}
