/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.mobile.web.jsf.datacollection.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementDetailDTO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementListDTO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollRequirementTagsListDTO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollCanEnterCollectionModeVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollRequirementDetailVO;
import com.jdx.rover.monitor.dto.mobile.datacollection.MobileDataCollTaskGetPlanRouteDTO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskGetPlanRouteVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskRemovePlanRouteVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskSavePlanRouteVO;
import com.jdx.rover.monitor.vo.mobile.datacollection.MobileDataCollTaskSceneAudioAssociateVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 小程序数据采集需求jsf服务接口
 *
 * <AUTHOR>
 * @date 2025/07/21
 */
public interface MobileDataCollRequirementJsfService {

    /**
     * 获取进行中的采集需求列表
     * @return 采集需求列表
     */
    HttpResult<List<MobileDataCollRequirementListDTO>> getRequirementList();

    /**
     * 获取采集需求详情
     * @param mobileDataCollRequirementDetailVO 采集需求详情入参
     * @return 采集需求详情
     */
    HttpResult<MobileDataCollRequirementDetailDTO> getRequirementDetail(@Valid @NotNull(message = "非法请求") MobileDataCollRequirementDetailVO mobileDataCollRequirementDetailVO);

    /**
     * 获取全量进行中的需求标签
     * @return 全量进行中的需求标签
     */
    HttpResult<MobileDataCollRequirementTagsListDTO> getRequirementTagsList();

    /**
     * 关联子任务语音信息
     * @param mobileDataCollTaskSceneAudioAssociateVO 关联子任务语音信息入参
     * @return HttpResult
     */
    HttpResult<Void> associateTaskSceneAudio(@Valid @NotNull(message = "非法请求") MobileDataCollTaskSceneAudioAssociateVO mobileDataCollTaskSceneAudioAssociateVO);

    /**
     * 保存规划路径
     * @param mobileDataCollTaskSavePlanRouteVO 规划路径入参
     * @return HttpResult
     */
    HttpResult<Void> saveTaskPlanRoute(@Valid @NotNull(message = "非法请求") MobileDataCollTaskSavePlanRouteVO mobileDataCollTaskSavePlanRouteVO);

    /**
     * 获取规划路径
     * @param mobileDataCollTaskGetPlanRouteVO 获取规划路径入参
     * @return 规划路径
     */
    HttpResult<List<MobileDataCollTaskGetPlanRouteDTO>> getTaskPlanRoute(@Valid @NotNull(message = "非法请求") MobileDataCollTaskGetPlanRouteVO mobileDataCollTaskGetPlanRouteVO);

    /**
     * 删除规划路径
     * @param mobileDataCollTaskRemovePlanRouteVO 删除规划路径入参
     */
    HttpResult<Void> removeTaskPlanRoute(@Valid @NotNull(message = "非法请求") MobileDataCollTaskRemovePlanRouteVO mobileDataCollTaskRemovePlanRouteVO);

    /**
     * 判断是否能进入采集模式
     * @param mobileDataCollCanEnterCollectionModeVO 判断是否能进入采集模式入参
     */
    HttpResult<Void> canEnterCollectionMode(@Valid @NotNull(message = "非法请求") MobileDataCollCanEnterCollectionModeVO mobileDataCollCanEnterCollectionModeVO);
}
