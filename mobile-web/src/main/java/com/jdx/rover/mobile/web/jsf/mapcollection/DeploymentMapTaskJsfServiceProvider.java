/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.mapcollection.service.DeploymentMapTaskJsfService;
import com.jdx.rover.monitor.dto.deployment.DeployEnumDTO;
import com.jdx.rover.monitor.dto.deployment.DeployTaskInfoDTO;
import com.jdx.rover.monitor.dto.deployment.DeployTaskListDTO;
import com.jdx.rover.monitor.dto.deployment.FuzzySearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.MapCollectionTaskExportResultDTO;
import com.jdx.rover.monitor.service.mapcollection.DeploymentMapTaskService;
import com.jdx.rover.monitor.service.mapcollection.MapCollectionTaskExportService;
import com.jdx.rover.monitor.vo.deployment.*;
import com.jdx.rover.monitor.vo.mapcollection.MapCollectionTaskExportVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 22:27
 * @description 部署平台线路勘查
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeploymentMapTaskJsfServiceProvider extends AbstractProvider<DeploymentMapTaskJsfService> implements DeploymentMapTaskJsfService {

    /**
     * DeploymentMapTaskService
     */
    private final DeploymentMapTaskService deploymentMapTaskService;

    /**
     * MapCollectionTaskExportService
     */
    private final MapCollectionTaskExportService mapCollectionTaskExportService;

    @Override
    @ServiceInfo(name = "获取部署枚举列表", webUrl = "/deployment/exploration/getEnumMapping")
    public HttpResult<List<DeployEnumDTO>> getEnumMapping() {
        return JsfResponse.response(deploymentMapTaskService::getEnumMapping);
    }

    @Override
    @ServiceInfo(name = "元素列表模糊搜索", webUrl = "/deployment/exploration/elementFuzzySearch")
    public HttpResult<List<FuzzySearchDTO>> elementFuzzySearch(FuzzySearchVO fuzzySearchVO) {
        return JsfResponse.response(() -> deploymentMapTaskService.elementFuzzySearch(fuzzySearchVO));
    }

    @Override
    @ServiceInfo(name = "获取部署地图元素列表", webUrl = "/deployment/exploration/getTaskRouteList")
    public HttpResult<DeployTaskListDTO> getTaskRouteList(DeployTaskListVO deployTaskListVO) {
        return JsfResponse.response(() -> deploymentMapTaskService.getTaskRouteList(deployTaskListVO));
    }

    @Override
    @ServiceInfo(name = "获取勘查任务详情", webUrl = "/deployment/exploration/getTaskDetail")
    public HttpResult<DeployTaskInfoDTO> getTaskDetail(TaskBaseVO taskBaseVO) {
        return JsfResponse.response(() -> deploymentMapTaskService.getTaskDetail(taskBaseVO));
    }

    @Override
    @ServiceInfo(name = "创建线路规划", webUrl = "/deployment/exploration/createTaskRoute")
    public HttpResult<Void> createTaskRoute(DeployTaskCreateVO deployTaskCreateVO) {
        return JsfResponse.response(() -> deploymentMapTaskService.createTaskRoute(deployTaskCreateVO));
    }

    @Override
    @ServiceInfo(name = "编辑线路规划", webUrl = "/deployment/exploration/modifyTaskRoute")
    public HttpResult<Void> modifyTaskRoute(DeployTaskEditVO deployTaskEditVO) {
        return JsfResponse.response(() -> deploymentMapTaskService.modifyTaskRoute(deployTaskEditVO));
    }

    @Override
    @ServiceInfo(name = "删除任务", webUrl = "/deployment/exploration/deleteTask")
    public HttpResult<Void> deleteTask(DeployTaskDeleteVO deployTaskDeleteVO) {
        return JsfResponse.response(() -> deploymentMapTaskService.deleteTask(deployTaskDeleteVO));
    }

    @Override
    @ServiceInfo(name = "撤回待认领任务", webUrl = "/deployment/exploration/revokeTask")
    public HttpResult<Void> revokeTask(TaskBaseVO taskBaseVO) {
        return JsfResponse.response(() -> deploymentMapTaskService.revokeTask(taskBaseVO));
    }

    @Override
    @ServiceInfo(name = "设置勘查任务状态（OPS）")
    public HttpResult<Void> refreshTaskStatus(List<Integer> taskIds, String taskStatus) {
        return JsfResponse.response(() -> deploymentMapTaskService.refreshTaskStatus(taskIds, taskStatus));
    }

    @Override
    @ServiceInfo(name = "勘查任务导出", webUrl = "/deployment/exploration/exportTaskData")
    public HttpResult<MapCollectionTaskExportResultDTO> exportMapCollectionTaskData(MapCollectionTaskExportVO exportVO) {
        return JsfResponse.response(() -> mapCollectionTaskExportService.exportMapCollectionTaskDataToS3(exportVO));
    }

    @Override
    @ServiceInfo(name = "勘查任务导出", webUrl = "/deployment/exploration/syncMapCollectionTask")
    public HttpResult<Void> syncMapCollectionTask(DeploySyncTaskVO deploySyncTaskVo) {
        return JsfResponse.response(() -> mapCollectionTaskExportService.exportMapCollectionTaskDataToDeployment(deploySyncTaskVo));
    }
}