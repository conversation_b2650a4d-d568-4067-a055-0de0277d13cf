package com.jdx.rover.monitor.api.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * 事故JMQ消息传输对象
 */
@Data
public class AccidentMessageDTO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 发生时间
     */
    private Date reportTime;

    /**
     * 恢复时间
     */
    private Date recoverTime;

    /**
     * 事故等级
     */
    private String accidentLevel;

    /**
     * 事故数字等级
     */
    private Integer accidentNumLevel;

    /**
     * 事故类型
     */
    private String accidentType;

    /**
     * 是否纳入统计
     */
    private Integer whetherIncludeStatistics;
}
