/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据采集需求分页查询请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataCollectionRequirementPageVO extends PageVO {

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 需求说明，模糊搜索
     */
    private String description;

    /**
     * 创建人erp
     */
    private String createUser;

    /**
     * 需求状态（ONGOING：进行中，FINISHED：已完成，CLOSED：已关闭）
     * @see com.jdx.rover.monitor.enums.datacollection.DataCollectionStatusEnum
     */
    private String status;
}
