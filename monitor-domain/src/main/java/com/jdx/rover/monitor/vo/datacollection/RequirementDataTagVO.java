/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 数据标签 VO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class RequirementDataTagVO {

    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不能为空")
    private Integer tagId;

    /**
     * 父级标签ID
     */
    private Integer parentId;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    private String tagName;

    /**
     * 标签类型
     * @see com.jdx.rover.monitor.enums.datacollection.DataCollectionTagTypeEnum
     */
    @NotBlank(message = "标签类型不能为空")
    private String tagType;

    /**
     * 标签需求数量
     */
    @NotNull(message = "标签需求数量不能为空")
    private Integer count;

    /**
     * 是否启用
     */
    @NotNull(message = "标签是否启用不能为空")
    private Boolean enabled;

    /**
     * 前端标签id
     */
    private Integer feId;

    /**
     * 前端父标签id
     */
    private Integer feParentId;
}
