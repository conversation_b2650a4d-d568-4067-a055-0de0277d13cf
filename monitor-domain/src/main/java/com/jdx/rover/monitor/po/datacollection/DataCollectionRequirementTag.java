/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.po.datacollection;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据采集需求关联标签表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("data_collection_requirement_tag")
public class DataCollectionRequirementTag extends BaseModel {

    /**
     * 需求id
     */
    private Integer requirementId;

    /**
     * 标签id
     */
    private Integer tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 父级标签id
     */
    private Integer parentTagId;

    /**
     * 前端标签id
     */
    private Integer feId;

    /**
     * 前端父标签id
     */
    private Integer feParentId;

    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 标签需求数量
     */
    private Integer count;

    /**
     * 是否启用
     */
    private Boolean enabled;
}
