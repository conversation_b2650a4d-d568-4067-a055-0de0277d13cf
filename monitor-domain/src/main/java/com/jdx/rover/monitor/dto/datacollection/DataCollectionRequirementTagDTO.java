/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.datacollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 数据采集需求关联标签 DTO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class DataCollectionRequirementTagDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 需求id
     */
    private Integer requirementId;

    /**
     * 标签id
     */
    private Integer tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 父级标签id
     */
    private Integer parentTagId;

    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 标签类型描述
     */
    private String tagTypeDescription;

    /**
     * 标签需求数量
     */
    private Integer count;

    /**
     * 是否启用
     */
    private Integer enabled;

    /**
     * 创建者
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后修改者
     */
    private String modifyUser;

    /**
     * 最近修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
}
