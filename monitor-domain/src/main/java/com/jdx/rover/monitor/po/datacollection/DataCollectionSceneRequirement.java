/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.po.datacollection;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数采场景关联需求 数据库表名为data_collection_scene_requirement
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "data_collection_scene_requirement", autoResultMap = true)
public class DataCollectionSceneRequirement extends BaseModel {

    /**
     * 场景标识
     */
    private Integer sceneId;

    /**
     * 需求标识
     */
    private Integer requirementId;

    /**
     * 是否关联
     */
    private Boolean linked;

    /**
     * 是否手动添加
     */
    private Boolean manual;

    /**
     * 重合度
     */
    private Integer overLap;

}