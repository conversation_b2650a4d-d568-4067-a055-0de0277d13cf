/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.mobile.datacollection;

import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionTagTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 获取采集需求详情DTO
 *
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
@Builder
public class MobileDataCollRequirementDetailDTO {

    /**
     * 需求描述
     */
    private String description;

    /**
     * 需求编号
     */
    private String requirementNumber;

    /**
     * 采集进度，保留两位小数
     */
    private Double progress;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;

    /**
     * 标签列表
     */
    private List<TagDTO> tagList;

    /**
     * 标签DTO
     */
    @Data
    @Builder
    public static class TagDTO {

        /**
         * 标签ID
         */
        private Integer tagId;

        /**
         * 父级标签ID
         */
        private Integer parentId;

        /**
         * 标签名称
         */
        private String tagName;

        /**
         * 标签类型
         * @see DataCollectionTagTypeEnum
         */
        private String tagType;
    }
}
