/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.enums.datacollection;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据采集标签类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Getter
@AllArgsConstructor
public enum DataCollectionTagTypeEnum {

    /**
     * 一级标签
     */
    MAIN("MAIN", "主标签"),

    /**
     * 二级标签
     */
    CHILDREN("CHILDREN", "关联标签"),

    /**
     * 细化标签
     */
    DETAIL("DETAIL", "细化标签");

    /**
     * 类型值
     */
    private final String value;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return DataCollectionTagTypeEnum
     */
    public static DataCollectionTagTypeEnum getByValue(String value) {
        for (DataCollectionTagTypeEnum tagType : values()) {
            if (tagType.getValue().equals(value)) {
                return tagType;
            }
        }
        return null;
    }

    /**
     * 根据值获取描述
     *
     * @param value 类型值
     * @return String
     */
    public static String getDescriptionByValue(String value) {
        DataCollectionTagTypeEnum tagType = getByValue(value);
        return tagType != null ? tagType.getDescription() : null;
    }
}
