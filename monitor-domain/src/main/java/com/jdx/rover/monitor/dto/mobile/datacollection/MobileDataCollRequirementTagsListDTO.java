/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.mobile.datacollection;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 获取全量未关闭需求标签DTO
 *
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileDataCollRequirementTagsListDTO {

    /**
     * 主标签名称集合
     */
    private Set<String> mainTagNameList;

    /**
     * 子标签名称集合
     */
    private Set<String> childrenTagNameList;

    /**
     * 细化标签名称集合
     */
    private Set<String> detailTagNameList;
}
