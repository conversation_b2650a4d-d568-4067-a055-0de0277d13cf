package com.jdx.rover.monitor.constant;

/**
 * <AUTHOR>
 */
public interface LocalCacheConstant {
  /**
   * 多车页筛选类型
   */
  String VEHICLE_NAME_SET_SELECT_TYPE = "vehicleNameSet:selectType";
  /**
   * 所有车辆名称有序队列
   */
  String ALL_VEHICLE_NAME_SORT_LIST = "all:vehicleNameSortList";
  /**
   * 所有车辆名称有序队列
   */
  String ALL_STATION_SORT_LIST = "all:stationSortList";
  /**
   * 用户名下车辆名称
   */
  String USER_VEHICLE_NAME_SET = "user:vehicleNameSet";
  /**
   * 用户关注车辆列表
   */
  String USER_ATTENTION_VEHICLE_NAME_SET = "user:attention:vehicleNameSet";
  /**
   * 驾舱下车辆名称
   */
  String COCKPIT_VEHICLE_NAME_SET = "cockpit:vehicleNameSet";

  /**
   * 用户名下停靠点
   */
  String USER_STOP_LIST = "user:stopList";

  /**
   * 用户名下站点
   */
  String USER_STATION_LIST = "user:stationList";

  /**
   * 手机号对应用户信息
   */
  String USER_NAME_PHONE = "user:phone:";
  /**
   * 车辆名称排序
   */
  String SORTED_SET_VEHICLE_NAME = "sortedSet:vehicleName";
  /**
   * 业务类型排序
   */
  String SORTED_SET_BUSINESS_TYPE = "sortedSet:businessType";
  /**
   * 电量排序
   */
  String SORTED_SET_POWER = "sortedSet:power";
  /**
   * 站点排序
   */
  String SORTED_SET_STATION = "sortedSet:station";
  /**
   * 车辆基础信息
   */
  String BASIC_VEHICLE = "basic:vehicle";
  /**
   * 调度信息
   */
  String SCHEDULE_VEHICLE = "schedule:vehicle";

  /**
   * 版本信息
   */
  String VEHICLE_TYPE = "vehicle:type";
  /**
   * 车辆启动模块配置
   */
  String VEHICLE_BOOT_MODULE = "vehicle:boot:module";
  /**
   * 用户车辆搜索记录
   */
  String USER_VEHICLE_SEARCH_RECORD_PREFIX = "user:vehicleSearch";

  /**
   * 车辆关联瓦片
   */
  String VEHICLE_TILE_URL = "vehicle:tile:url";

  /**
   * PDA总数
   */
  String PDA_DEVICE_TOTAL_COUNT = "pda:device:total:count";

  /**
   * 错误码映射列表
   */
  String ERROR_CODE_TRANSLATE = "error:code:translateList";

  /**
   * 分组信息
   */
  String ROBOT_GROUP_INFO = "robot:group:info";

  /**
   * 告警信息
   */
  String ROBOT_ABNORMAL_INFO = "robot:abnormal:info";

  /**
   * 告警停车信息
   */
  String ROBOT_STOP_INFO = "robot:stop:info";

  /**
   * 告警信息
   */
  String ROBOT_ISSUE_INFO = "robot:issue:info";

  /**
   * 调度任务信息
   */
  String ROBOT_SCHEDULE_INFO = "robot:schedule:info";

  /**
   * 设备分组号信息
   */
  String ROBOT_DEVICE_GROUP_NO = "robot:device:group:no";

  /**
   * 停用车辆列表
   */
  String DISABLE_DEVICE_NAME_SET = "disable:robot:deviceNameSet";

  /**
   * 机器人筛选类型
   */
  String ROBOT_NAME_SET_SELECT_TYPE = "robotNameSet:selectType";

  /**
   * 机器人拥堵信息
   */
  String ROBOT_CONGESTION_INFO = "robot:congestion:info";

  /**
   * 机器人任务路线信息。
   */
  String ROBOT_TASK_ROUTE_INFO = "robot:task:route";

  /**
   * 数采车实时场景
   */
  String DATA_COLLECTION_VEHICLE_SCENE = "data:collection:vehicle:scene";

  /**
   * 所有数采标签列表
   */
  String DATA_COLLECTION_TAG_LIST = "all:data:collection:tagList";

  /**
   * 车辆控制紧急制动
   */
  String VEHICLE_CONTROL_GUARDIAN_AEB = "vehicle:control:guardian:aeb";
}