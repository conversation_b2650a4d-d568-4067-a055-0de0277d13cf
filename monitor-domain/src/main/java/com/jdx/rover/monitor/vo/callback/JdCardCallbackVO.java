package com.jdx.rover.monitor.vo.callback;

import lombok.Data;

/**
 * 京ME消息卡片回调请求对象
 * <a href="https://joyspace.jd.com/pages/gOkCgN1WGAzGgLZhG0s2">回传文档</a>
 */
@Data
public class JdCardCallbackVO {

    private Integer reqType;

    private FromVO from;

    private ToVO to;

    private String gid;

    private Long dateTime;

    private String token;

    private String lang;

    private BodyVO body;

    @Data
    public static class FromVO {
        private String app;
        private String pin;
        private String clientType;
    }

    @Data
    public static class ToVO {
        private String app;
        private String pin;
    }

    @Data
    public static class BodyVO {
        private String templateId;
        private String cardMsgId;
        private Integer sessionType;
        private String sessionId;
        private ActionDataVO actionData;
    }

    @Data
    public static class ActionDataVO {
        /**
         * 业务类型，事故-accident, 维修-repair, 后期业务增加再写枚举
         */
        private String businessType;

        /**
         * 操作类型, 恢复运营-recover, 后期增加再写枚举
         */
        private String operatorType;

        /**
         * 编号，事故编号-accidentNo
         */
        private String number;

        /**
         * 环境 用于区分不同环境
         */
        private String env;
    }
}
