/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.po.datacollection;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数采场景关联标签 数据库表名为data_collection_scene_tag
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "data_collection_scene_tag", autoResultMap = true)
public class DataCollectionSceneTag extends BaseModel {

    /**
     * 场景标识
     */
    private Integer sceneId;

    /**
     * 标签标识
     */
    private Integer tagId;

    /**
     * 标签名称
     */
    private String tagName;
}
