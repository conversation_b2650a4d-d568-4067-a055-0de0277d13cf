/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.po.datacollection;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数采场景 数据库表名为data_collection_scene
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "data_collection_scene", autoResultMap = true)
public class DataCollectionScene extends BaseModel {

    /**
     * 车辆名称，用于标识数采场景所属的车辆
     */
    private String vehicleName;

    /**
     * 场景编号
     */
    private String sceneNumber;

    /**
     * 记录数采场景的开始时间
     */
    private Date startTime;

    /**
     * 记录数采场景的结束时间
     */
    private Date endTime;

    /**
     * 记录数采场景生成时间
     */
    private Date reportTime;

    /**
     * 表示数采场景的当前状态
     */
    private String status;

    /**
     * 音频文件在存储系统中地址
     */
    private String audioFileKey;

    /**
     * 行驶路径
     */
    private String drivePath;

    /**
     * 音频识别结果
     */
    private String audioRecognitionResult;

    /**
     * 音频识别关键词
     */
    private String audioRecognitionKeyWords;

}