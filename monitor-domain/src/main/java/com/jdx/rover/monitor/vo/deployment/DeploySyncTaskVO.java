/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.deployment;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 22:12
 * @description 同步线路任务
 */
@Data
public class DeploySyncTaskVO {

    /**
     * 站点任务列表
     */
    private List<StationTask> stationTaskList;

    @Data
    public static class StationTask {
        /**
         * 所属站点ID
         */
        private Integer stationId;

        /**
         * 需求编号
         */
        private String requireNumber;

        /**
         * 供应商
         */
        private String supplier;

        /**
         * 操作类型
         */
        private String operation;
    }
}