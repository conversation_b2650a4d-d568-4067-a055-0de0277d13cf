/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.datacollection;

import lombok.Data;

import java.util.List;

/**
 * 数据采集需求场景 DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class DataCollectionRequirementDetailScene {

    /**
     * 场景ID
     */
    private Integer sceneId;

    /**
     * 场景编号
     */
    private String sceneNumber;

    /**
     * 标签名称列表
     */
    private List<String> tagNameList;

    /**
     * 车牌号
     */
    private String vehicleName;
}
