/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更改数据采集需求状态请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class DataCollectionRequirementChangeStateVO {

    /**
     * 需求ID
     */
    @NotNull(message = "需求ID不能为空")
    private Integer requirementId;

    /**
     * 需求状态操作类型（OPEN：开启，CLOSE：关闭）
     */
    @NotBlank(message = "操作类型不能为空")
    private String actionType;
}
