package com.jdx.rover.monitor.enums.redis;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RedisTopicEnum {
  SINGLE_VEHICLE_PREFIX("single:vehicle:", "prefix", "单车页信息"),
  SINGLE_VEHICLE_DRIVABLE_PREFIX("single:vehicle:drivable:", "prefix", "单车页使能信息"),
  SINGLE_VEHICLE_EXCEPTION_PREFIX("single:vehicle:exception:", "prefix", "单车页异常信息"),
  SINGLE_VEHICLE_OPERATION_PREFIX("single:vehicle:operation:", "prefix", "单车页操作信息"),
  SINGLE_VEHICLE_ALARM_PREFIX("single:vehicle:alarm:", "prefix", "单车页告警信息"),
  MULTI_VEHICLE_PREFIX("multi:vehicle:", "prefix", "多车页信息(暂不实现推送)"),
  ALARM_EVENT_PREFIX("alarm:event:", "prefix", "告警信息"),
  LOCAL_VIEW_PREFIX("local:view:", "prefix", "3D信息"),
  MAP_VEHICLE_SCHEDULE_PREFIX("map:vehicle:schedule:", "prefix", "地图页车辆调度信息"),
  MAP_VEHICLE_POSITION_PREFIX("map:vehicle:position:", "prefix", "地图页车辆位置信息"),
  REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX("remote:vehicle:response:", "prefix", "遥控页车辆响应信息"),
  VEHICLE_ALARM_RECORD("vehicle:alarm:record", "all", "车辆告警记录"),
  VEHICLE_ISSUE_ALARM_RECORD("vehicle:issue:alarm:record", "all", "车辆工单告警"),
  LOCAL_CACHE_EVICT("local:cache:evict", "all", "本地缓存过期通知"),
  ISSUE_EVENT_PREFIX("issue:event:", "prefix", "工单信息"),
  REMOTE_CONTROL_WEB_TERMINAL_PREFIX("remote:web:terminal:response:", "prefix", "远程工具连接"),
  MAP_VARIABLE_VERSION_UPGRADE("map:variable:version:upgrade", "all", "更新动态地图本地缓存"),
  DATA_COLLECTION_TASK_VEHICLE("data:collection:task:vehicle:", "prefix", "数据采集采集任务车辆信息通知"),

  REMOTE_CONTROL_CLOUD_MAP_PREFIX("remote:cloud:map:response:", "prefix", "人工辅助定位连接"),

  MAP_ROBOT_POSITION_PREFIX("map:robot:position:", "prefix", "地图页机器人位置信息"),
  MAP_ROBOT_CONGESTION_PREFIX("map:robot:congestion:", "prefix", "地图页拥堵信息"),
  ;

  /**
   * 值
   */
  private final String value;

  /**
   * prefix前缀,suffix后缀,all全部
   */
  private final String type;

  /**
   * 标题描述
   */
  private final String title;
}
