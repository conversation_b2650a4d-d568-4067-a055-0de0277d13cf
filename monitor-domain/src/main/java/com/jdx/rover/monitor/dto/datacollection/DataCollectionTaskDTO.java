/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.datacollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数采车质检任务
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionTaskDTO {

    /**
     * 任务编号
     */
    private Integer id;

    /**
     * 行驶路径
     */
    private String drivePath;

    /**
     * 车号
     */
    private List<DataCollectionTaskLinkScene> sceneList;

    /*
       质检任务关联的场景列表
     */
    @Data
    public static class DataCollectionTaskLinkScene {

        /**
         * 场景标识Id
         */
        private Integer sceneId;

        /**
         * 场景任务状态
         *         PROCESSING：处理中
         *         FINISHED：已完成
         */
        private String status;

        /**
         * 场景路段(geoString)
         */
        private String path;

        /**
         * 场景开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date startTime;

        /**
         * 场景结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date endTime;

        /**
         * 场景上报时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date reportTime;

    }

}