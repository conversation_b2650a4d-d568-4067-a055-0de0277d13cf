/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.dto.cockpit;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 座席下车辆列表
 *
 * <AUTHOR>
 * @date 2023/06/07
 */
@Data
@NoArgsConstructor
public class CockpitSingleVehicleDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 车辆模式
     */
    private String vehicleState;

    /**
     * 调度状态
     */
    private String scheduleState;

    /**
     * 系统状态
     */
    private String systemState;

    /**
     * 电量
     */
    private Float power;

    /**
     * 接管人名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String takeOverUserName;

    /**
     * 接管座舱编号
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String takeOverCockpitNumber;

    /**
     * 接管来源
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String takeOverSource;

    /**
     * 接管状态(临时停车还是接管)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String takeOverStatus;

    /**
     * 规划总里程
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double globalMileage;

    /**
     * 已走里程
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Double arrivedMileage;

    /**
     * 告警事件列表
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> alarmEventNameList;

    /**
     * 运行地图状态
     */
    private String runMapState;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * gps信号
     */
    private Integer gpsSignal;

    /**
     * 座席状态
     */
    private String cockpitStatus;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 朝向
     */
    private Double heading;

    /**
     * 车辆实际朝向
     */
    private Double realHeading;

    /**
     * 定位置信度
     */
    private Double sceneSignal;

    /**
     * 前向使能
     */
    private Boolean enableFront;

    /**
     * 后向使能
     */
    private Boolean enableBack;

    /**
     * 左向使能
     */
    private Boolean enableLeft;

    /**
     * 右向使能
     */
    private Boolean enableRight;

    /**
     * 任务绑定状态
     */
    private Boolean bindingState;

    /**
     * 关联采图任务
     */
    private Integer taskId;

    /**
     * 关联采图任务
     */
    private String taskName;

    /**
     * 关联采图状态
     */
    private String taskStatus;

    /**
     * 是否处于切换状态
     */
    private Boolean isSwitchStatus;

    /**
     * 车辆采集模式
     */
    private String collectionMode;

    /**
     * 采集状态
     */
    private String collectionStatus;

    /**
     * 子任务数量
     */
    private Integer subTaskQuantity;

    /**
     * 今日采集用时，单位：分钟
     */
    private Integer workingTime;

    /**
     * 标识车辆是否处于需求接管状态
     */
    private Boolean isUnderRequire;

    /**
     * 信号延时，单位ms
     */
    private Double signalConnectivityDelay;
}