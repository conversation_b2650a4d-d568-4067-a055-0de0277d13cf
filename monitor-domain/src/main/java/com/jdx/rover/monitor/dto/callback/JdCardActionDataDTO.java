package com.jdx.rover.monitor.dto.callback;

import lombok.Data;

@Data
public class JdCardActionDataDTO {

    /**
     * 业务类型，事故-accident, 维修-repair, 后期业务增加再写枚举
     */
    private String businessType;

    /**
     * 操作类型, 恢复运营-recover, 后期增加再写枚举
     */
    private String operatorType;

    /**
     * 编号，事故编号-accidentNo
     */
    private String number;

    /**
     * 操作人erp
     */
    private String userErp;

    /**
     * 环境
     */
    private String env;
}
