/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection.ws;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据采集采集任务车辆topic消息
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataCollectionTaskVehicleTopicMsg {

    /**
     * 消息类型
     * @see com.jdx.rover.monitor.enums.datacollection.DataCollectionSseMsgTypeEnum
     */
    private String messageType;

    /**
     * 新用户名
     */
    private String username;

    /**
     * 场景ID
     */
    private Integer sceneId;

    /**
     * 拨杆时间
     */
    private Date leverTime;
}
