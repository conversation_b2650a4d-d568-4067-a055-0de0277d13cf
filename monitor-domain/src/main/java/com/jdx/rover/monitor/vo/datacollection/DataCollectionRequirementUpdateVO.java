/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

/**
 * 更新数据采集需求请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class DataCollectionRequirementUpdateVO {

    /**
     * 需求编号
     */
    @NotBlank(message = "需求编号不能为空")
    private String requirementNumber;

    /**
     * 需求状态
     */
    private String status;

    /**
     * 任务进度
     */
    @DecimalMin(value = "0.0", message = "进度不能小于0")
    @DecimalMax(value = "100.0", message = "进度不能大于100")
    private Double progress;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;
}
