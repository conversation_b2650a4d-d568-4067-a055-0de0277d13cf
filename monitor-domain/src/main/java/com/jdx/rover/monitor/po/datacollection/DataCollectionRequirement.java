/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.po.datacollection;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据采集需求表
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("data_collection_requirement")
public class DataCollectionRequirement extends BaseModel {

    /**
     * 需求编号
     */
    private String requirementNumber;

    /**
     * 需求状态
     */
    private String status;

    /**
     * 需求说明
     */
    private String description;

    /**
     * 任务进度
     */
    private Double progress;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;
}
