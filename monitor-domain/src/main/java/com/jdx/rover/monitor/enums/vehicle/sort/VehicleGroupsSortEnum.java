/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.sort;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 接口返回值风格样式枚举类
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleGroupsSortEnum {
    // 失联
    CONNECTION_LOST(VehicleCategoryEnum.CONNECTION_LOST.getStartScore(), VehicleCategoryEnum.CONNECTION_LOST.getValue(), "失联"),

    // 碰撞
    VEHICLE_CRASH(VehicleCategoryEnum.VEHICLE_CRASH.getStartScore(), VehicleCategoryEnum.VEHICLE_CRASH.getValue(), "碰撞"),

    // 硬件异常
    DOMAIN_CONTROLLER_ALARM(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore(), VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "域控告警"),
    RADAR_WARNING(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 1000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "雷达预警"),
    RADAR_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 2000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "雷达故障"),
    AUDIBLE_AND_VISUAL_ALARM_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 3000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "声光电故障"),
    EDGE_SENSOR_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 4000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "触边故障"),
    BATTERY_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 5000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "电池故障"),
    BRAKE_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 6000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "刹车故障"),
    PARKING_BRAKE_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 7000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "驻车故障"),
    STEERING_EPS_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 8000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "转向EPS故障"),
    ELECTRONIC_CONTROL_MOTOR_FAILURE(VehicleCategoryEnum.HARDWARE_ERROR.getStartScore() + 9000, VehicleCategoryEnum.HARDWARE_ERROR.getValue(), "电控、电机故障"),

    // 网络信号
    SIM_CARD_ABNORMAL(VehicleCategoryEnum.NETWORK_SIGNAL.getStartScore(), VehicleCategoryEnum.NETWORK_SIGNAL.getValue(), "SIM卡异常"),
    CONNECTIVITY_FAILURE(VehicleCategoryEnum.NETWORK_SIGNAL.getStartScore() + 1000, VehicleCategoryEnum.NETWORK_SIGNAL.getValue(), "连通性失败"),

    // 人工告警
    MANUAL_REPORT(VehicleCategoryEnum.MANUAL_REPORT.getStartScore(), VehicleCategoryEnum.MANUAL_REPORT.getValue(), "人工告警"),

    // 传感器异常
    LOCALIZATION_ERROR(VehicleCategoryEnum.SENSOR_ERROR.getStartScore(), VehicleCategoryEnum.SENSOR_ERROR.getValue(), "定位异常"),
    SENSOR_ERROR(VehicleCategoryEnum.SENSOR_ERROR.getStartScore() + 5000, VehicleCategoryEnum.SENSOR_ERROR.getValue(), "传感器异常"),

    // 运营告警
    OPERATION_ALARM(VehicleCategoryEnum.OPERATION_ALARM.getStartScore(), VehicleCategoryEnum.OPERATION_ALARM.getValue(), "运营报警"),

    // 开机异常
    BOOT_ABNORMAL_FAIL(VehicleCategoryEnum.BOOT_ALARM.getStartScore(), VehicleCategoryEnum.BOOT_ALARM.getValue(), "异常重启失败"),
    BOOT_ABNORMAL(VehicleCategoryEnum.BOOT_ALARM.getStartScore() + 5000, VehicleCategoryEnum.BOOT_ALARM.getValue(), "异常重启"),
    BOOT_FAIL(VehicleCategoryEnum.BOOT_ALARM.getStartScore() + 10000, VehicleCategoryEnum.BOOT_ALARM.getValue(), "开机失败"),
    BOOT_TIMEOUT(VehicleCategoryEnum.BOOT_ALARM.getStartScore() + 15000, VehicleCategoryEnum.BOOT_ALARM.getValue(), "开机过久"),

    // 路口异常
    PASS_NO_SIGNAL_INTERSECTION(VehicleCategoryEnum.INTERSECTION_ABNORMAL.getStartScore(), VehicleCategoryEnum.INTERSECTION_ABNORMAL.getValue(), "无保护左转"),
    VEHICLE_STOP_TRAFFICLIGHT_TAKEUP(VehicleCategoryEnum.INTERSECTION_ABNORMAL.getStartScore() + 5000, VehicleCategoryEnum.INTERSECTION_ABNORMAL.getValue(), "看灯点被占"),
    VEHICLE_STOP_TRAFFICLIGHT_FAIL(VehicleCategoryEnum.INTERSECTION_ABNORMAL.getStartScore() + 10000, VehicleCategoryEnum.INTERSECTION_ABNORMAL.getValue(), "感知看灯失败"),
    VEHICLE_STOP_TRAFFICLIGHT_ADJUST(VehicleCategoryEnum.INTERSECTION_ABNORMAL.getStartScore() + 15000, VehicleCategoryEnum.INTERSECTION_ABNORMAL.getValue(), "看灯规划失败"),
    VEHICLE_STOP_INTERSECTION_STUCK(VehicleCategoryEnum.INTERSECTION_ABNORMAL.getStartScore() + 20000, VehicleCategoryEnum.INTERSECTION_ABNORMAL.getValue(), "路口遇阻"),

    // 停车
    VEHICLE_STOP_BUTTON(VehicleCategoryEnum.STOP.getStartScore(), VehicleCategoryEnum.STOP.getValue(), "按钮停车"),
    GATE_STUCK(VehicleCategoryEnum.STOP.getStartScore() + 5000, VehicleCategoryEnum.STOP.getValue(), "过门遇阻"),
    VEHICLE_STOP_STOP_TAKEUP(VehicleCategoryEnum.STOP.getStartScore() + 15000, VehicleCategoryEnum.STOP.getValue(), "停靠点被占"),
    VEHICLE_STOP_STOP_ADJUST(VehicleCategoryEnum.STOP.getStartScore() + 20000, VehicleCategoryEnum.STOP.getValue(), "停靠规划失败"),
    VEHICLE_STOP_FATAL(VehicleCategoryEnum.STOP.getStartScore() + 25000, VehicleCategoryEnum.STOP.getValue(), "异常停车"),
    CPU_HIGH_TEMPERATURE(VehicleCategoryEnum.STOP.getStartScore() + 30000, VehicleCategoryEnum.STOP.getValue(), "CPU温度过高"),
    ROUTING_PLAN_FAIL(VehicleCategoryEnum.STOP.getStartScore() + 35000, VehicleCategoryEnum.STOP.getValue(), "Routing规划失败"),
    VEHICLE_STOP_GUARDIAN(VehicleCategoryEnum.STOP.getStartScore() + 40000, VehicleCategoryEnum.STOP.getValue(), "安全接管停车"),
    PARK_HARD(VehicleCategoryEnum.STOP.getStartScore() + 45000, VehicleCategoryEnum.STOP.getValue(), "停靠困难"),
    VEHICLE_STOP_TIMEOUT(VehicleCategoryEnum.STOP.getStartScore() + 50000, VehicleCategoryEnum.STOP.getValue(), "遇阻停车"),

    // 区域预警
    ATTENTION_REGION_WARN(VehicleCategoryEnum.CLOUD_WARN.getStartScore(), VehicleCategoryEnum.CLOUD_WARN.getValue(), "关注预警"),
    PARKING_REGION_WARN(VehicleCategoryEnum.CLOUD_WARN.getStartScore() + 5000, VehicleCategoryEnum.CLOUD_WARN.getValue(), "停车预警"),

    // 电量
    LOW_BATTERY(VehicleCategoryEnum.LOW_BATTERY.getStartScore(), VehicleCategoryEnum.STOP.getValue(), "电量低"),

    // 胎压
    TIRE_PRESSURE_SENSOR_OFFLINE(VehicleCategoryEnum.LOW_PRESSURE.getStartScore(), VehicleCategoryEnum.LOW_PRESSURE.getValue(), "胎压传感器离线"),
    PRESSURE_SHARP_DECREASE(VehicleCategoryEnum.LOW_PRESSURE.getStartScore() + 5000, VehicleCategoryEnum.LOW_PRESSURE.getValue(), "胎压骤减"),
    LOW_PRESSURE(VehicleCategoryEnum.LOW_PRESSURE.getStartScore() + 10000, VehicleCategoryEnum.LOW_PRESSURE.getValue(), "胎压异常"),

    NORMAL(VehicleCategoryEnum.NORMAL.getStartScore(), VehicleCategoryEnum.NORMAL.getValue(), "正常"),
    OFFLINE(VehicleCategoryEnum.OFFLINE.getStartScore(), VehicleCategoryEnum.OFFLINE.getValue(), "离线"),
    ;

    private final Double value;

    /**
     * 分类
     */
    private final String category;

    private final String title;
}

