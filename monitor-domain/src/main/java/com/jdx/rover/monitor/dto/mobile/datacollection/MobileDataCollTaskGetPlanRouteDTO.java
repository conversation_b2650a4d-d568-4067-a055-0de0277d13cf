/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.mobile.datacollection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取规划路径出参
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MobileDataCollTaskGetPlanRouteDTO {

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;
}
