/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.po.mapcollection;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.domain.jsonhandler.FourLaneListTypeHandler;
import com.jdx.rover.monitor.domain.jsonhandler.StringListTypeHandler;
import com.jdx.rover.monitor.domain.jsonhandler.TaskRouteListTypeHandler;
import com.jdx.rover.monitor.po.mapcollection.json.FourLaneInfo;
import com.jdx.rover.monitor.po.mapcollection.json.TaskRoutePoint;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:13
 * @description 勘查任务表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "map_collection_task", autoResultMap = true)
public class MapCollectionTask extends BaseModel {

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态
     * @see com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum
     */
    private String taskStatus;

    /**
     * 任务编号
     */
    private String taskNumber;

    /**
     * 任务创建人
     */
    private String taskCreator;

    /**
     * 线路类型
     */
    private String taskRouteType;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 勘查线路
     */
    @TableField(typeHandler = TaskRouteListTypeHandler.class)
    private List<TaskRoutePoint> taskRoute;

    /**
     * 勘查线路总里程，单位千米，精度小数点后两位
     */
    private Double totalMileage;

    /**
     * 线路颜色，默认蓝
     * @see com.jdx.rover.monitor.enums.mapcollection.RouteColorEnum
     */
    private String routeColor;

    /**
     * 四车道
     */
    @TableField(typeHandler = FourLaneListTypeHandler.class)
    private List<FourLaneInfo> fourLane;

    /**
     * 线路名称列表
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> roadNames;

    /**
     * 线路方案
     * @see com.jdx.rover.monitor.enums.mapcollection.RoutePlanTypeEnum
     */
    private String routePlanType;

    /**
     * 精度方案
     * @see com.jdx.rover.monitor.enums.mapcollection.PreciseTypeEnum
     */
    private String preciseType;

    /**
     * 采集车辆名称
     */
    private String vehicleName;

    /**
     * 任务提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskSubmitTime;
}