package com.jdx.rover.monitor.dto.accident;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 事故详情传输对象
 */
@Data
public class MonitorAccidentDetailDTO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 事故提报时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accidentTime;

    /**
     * 描述
     */
    private String accidentDesc;

    /**
     * 事故来源
     */
    private String accidentSource;

    /**
     * 事故来源名称
     */
    private String accidentSourceName;

    /**
     * 事故地址
     */
    private String accidentAddress;

    /**
     * 缺陷编号
     */
    private String bugCode;

    /**
     * 一线跟进人
     */
    private String operationUser;

    /**
     * 事故快照
     */
    private List<MonitorAccidentAttachmentDTO> snapshotAttachmentUrlList;

    /**
     * 处理方式
     */
    private String operationHandleMethodName;

    /**
     * 是否需要赔偿
     */
    private String operationCompensatedName;

    /**
     * 金额
     */
    private String operationAmount;

    /**
     * 是否上报车网
     */
    private String operationIsReportVehicleNetName;

    /**
     * 事故分类
     */
    private String operationAccidentTypeName;

    /**
     * 事故责任
     */
    private String operationAccidentJudgeName;

    /**
     * 原因
     */
    private String operationAccidentReason;

    /**
     * 运营附件
     */
    private List<MonitorAccidentAttachmentDTO> operationAttachmentUrlList;

    /**
     * 操作日志
     */
    private List<MonitorAccidentOperateDTO> opertaionLogList;

    /**
     * 技术支持类型
     */
    private String technicalSupportAccidentType;

    /**
     * 技术支持等级
     */
    private String technicalSupportAccidentLevel;

    /**
     * 技术支持描述
     */
    private String technicalSupportDescription;

    /**
     * 技术支持附件
     */
    private List<MonitorAccidentAttachmentDTO> technicalSupportAttmentUrlList;

    /**
     * 安全组定责事故分类
     */
    private String safetyGroupAccidentType;

    /**
     * 安全组定责事故等级
     */
    private String safetyGroupAccidentLevel;

    /**
     * 安全组描述
     */
    private String safetyGroupDescription;

    /**
     * 事故模块
     */
    private List<Integer> safetyGroupAccidentModuleList;

    /**
     * 事故标签
     */
    private List<Integer> safetyGroupAccidentTagList;

    /**
     * 解决方案
     */
    private String safetyGroupAccidentSolution;

    /**
     * 挂起原因
     */
    private String safetyGroupAccidentSuspendReason;

    /**
     * 事故报告
     */
    private List<MonitorAccidentAttachmentDTO> safetyGroupAttmentUrlList;

    /**
     * 缺陷状态
     */
    private String bugStatus;

    /**
     * 缺陷状态名称
     */
    private String bugStatusName;

    /**
     * 事故负责人
     */
    private String accidentOwner;

    /**
     * 视频截取状态
     */
    private String videoCaptureStatus;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 是否纳入事故统计
     */
    private Integer whetherIncludeStatistics;

    /**
     * 是否纳入事故统计名称
     */
    private String whetherIncludeStatisticsName;
}
