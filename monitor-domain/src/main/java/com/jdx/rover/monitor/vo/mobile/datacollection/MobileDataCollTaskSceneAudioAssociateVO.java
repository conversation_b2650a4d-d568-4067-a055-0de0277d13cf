/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.mobile.datacollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 关联子任务语音信息入参
 *
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
public class MobileDataCollTaskSceneAudioAssociateVO {

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String vehicleName;

    /**
     * 场景任务ID
     */
    @NotNull(message = "场景任务ID不能为空")
    private Integer sceneId;

    /**
     * 录音fileKey
     */
    private String audioFileKey;

    /**
     * 语音识别结果
     */
    private String speechRecognitionResult;
}
