/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.mobile.datacollection;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 京小鸽数据采集进入采集模式入参
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Data
public class MobileDataCollCanEnterCollectionModeVO {

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String vehicleName;
}
