/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.datacollection;

import lombok.Data;

import java.util.List;

/**
 * 数据采集场景详情 DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class DataCollectionSceneDetailDTO {

    /**
     * 场景编号
     */
    private String sceneNumber;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 标签名称列表
     */
    private List<DataCollectionTagDTO> tagList;
}
