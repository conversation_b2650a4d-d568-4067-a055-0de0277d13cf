package com.jdx.rover.monitor.enums.accident;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: 京me事故卡片标题枚举
 * @author: do<PERSON><PERSON><PERSON>
 * @date: 2025/8/21
 */

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AccidentNewStatusEnum {

    INFLUENCE_OPERATION("INFLUENCE_OPERATION","影响运营"),
    RECOVER_OPERATION("RECOVER_OPERATION","恢复运营"),
    VEHICLE_REPAIR("VEHICLE_REPAIR","车辆维修"),
    ;

    /**
     * 值.
     */
    private final String value;

    /**
     * 标题.
     */
    private final String title;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentNewStatusEnum accidentNewStatusEnum : AccidentNewStatusEnum.values()) {
            if (accidentNewStatusEnum.getValue().equals(value)) {
                return accidentNewStatusEnum.getTitle();
            }
        }
        return null;
    }
}
