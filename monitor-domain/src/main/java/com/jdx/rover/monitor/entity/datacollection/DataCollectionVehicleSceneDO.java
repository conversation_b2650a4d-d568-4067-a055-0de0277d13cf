/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.datacollection;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 数据采集车辆场景DO
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionVehicleSceneDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 场景编号
     */
    private String sceneNumber;

    /**
     * 场景上报时间
     */
    private Date reportTime;

    /**
     * 场景开始时间
     */
    private Date startTime;

    /**
     * 场景结束时间
     */
    private Date endTime;
}
