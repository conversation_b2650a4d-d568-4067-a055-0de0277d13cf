/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.datacollection;

import lombok.Data;

import java.util.List;

/**
 * 数采车场景
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionSceneDTO {

    /**
     * 场景标识
     */
    private Integer sceneId;

    /**
     * 场景编号
     */
    private String sceneNumber;

    /**
     * 场景状态
     */
    private String status;

    /**
     * 前路视频url
     */
    private String frontVideoUrl;

    /**
     * 语音url
     */
    private String audioFileKey;

    /**
     * 语音识别结果
     */
    private String audioRecognitionResult;

    /**
     * 识别关键字
     */
    private List<String> keywords;

    /**
     * 关联标签列表
     */
    private List<DataCollectionTag> matchedTagList;

    /*
       场景关联的标签列表
     */
    @Data
    public static class DataCollectionTag {

        /**
         * 标签Id
         */
        private Integer tagId;

        /**
         * 标签名称
         */
        private String tagName;

    }

}