/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.po.datacollection;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数采任务 数据库表名为data_collection_task
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "data_collection_task", autoResultMap = true)
public class DataCollectionTask extends BaseModel {

    /**
     * 采集车辆名称
     */
    private String vehicleName;

    /**
     * 采集任务状态
     */
    private String status;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 采集地址
     */
    private String address;

    /**
     * 行驶路径
     */
    private String drivePath;

    /**
     * 是否关联场景
     */
    private boolean linkedScene;

}