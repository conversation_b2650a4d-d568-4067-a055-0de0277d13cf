/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.enums.datacollection;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据采集场景质检状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Getter
@AllArgsConstructor
public enum DataCollectionSceneStatusEnum {

    /**
     * 待质检
     */
    STANDBY ("STANDBY", "待质检"),
    /**
     * 已完成
     */
    FINISHED("FINISHED", "完成质检")

    ;


    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return DataCollectionSceneStatusEnum
     */
    public static DataCollectionSceneStatusEnum getByValue(String value) {
        for (DataCollectionSceneStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

}