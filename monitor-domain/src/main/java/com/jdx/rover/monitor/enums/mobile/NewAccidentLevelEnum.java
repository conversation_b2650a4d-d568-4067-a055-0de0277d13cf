package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum NewAccidentLevelEnum {

    NONE("NONE", "无责", -1),
    S0("S0", "有责-S0", 0),
    S1("S1", "有责-S1", 1),
    S2("S2", "有责-S2", 2),
    S3("S3", "有责-S3", 3),
    S4("S4", "有责-S4", 4),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * 别名
     */
    private final Integer alisa;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (NewAccidentLevelEnum newAccidentLevelEnum : NewAccidentLevelEnum.values()) {
            if (newAccidentLevelEnum.getValue().equals(value)) {
                return newAccidentLevelEnum.getName();
            }
        }
        return null;
    }


    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getValueByName(String name) {
        for (NewAccidentLevelEnum newAccidentLevelEnum : NewAccidentLevelEnum.values()) {
            if (newAccidentLevelEnum.getName().equals(name)) {
                return newAccidentLevelEnum.getValue();
            }
        }
        return null;
    }

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static Integer getAlisaByValue(String value) {
        for (NewAccidentLevelEnum newAccidentLevelEnum : NewAccidentLevelEnum.values()) {
            if (newAccidentLevelEnum.getValue().equals(value)) {
                return newAccidentLevelEnum.getAlisa();
            }
        }
        return null;
    }
}
