/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection.ws;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据采集WS车辆拨杆消息
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataCollectionWsVehicleLever {

    /**
     * 场景ID
     */
    private Integer sceneId;

    /**
     * 拨杆时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date createTime;
}
