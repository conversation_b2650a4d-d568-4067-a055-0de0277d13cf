/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 数据采集需求列表查询请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
public class DataCollectionRequirementListVO {

    /**
     * 开始时间，必填，默认当天00:00:00
     */
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /**
     * 结束时间，必填，默认当天23:59:59
     */
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    /**
     * 需求说明，模糊搜索
     */
    private String description;

}