/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.datacollection;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数据采集需求详情响应 DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class DataCollectionRequirementDetailDTO {

    /**
     * 需求ID
     */
    private Integer requirementId;

    /**
     * 需求编号
     */
    private String requirementNumber;

    /**
     * 需求说明
     */
    private String description;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;

    /**
     * 创建时间，yyyy-MM-dd HH:mm:ss
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 需求状态（ONGOING：进行中，FINISHED：已完成，CLOSED：已关闭）
     * @see com.jdx.rover.monitor.enums.datacollection.DataCollectionStatusEnum
     */
    private String status;

    /**
     * 需求进度，保留两位小数，无关联标签，返回null
     */
    private Double progress;

    /**
     * 关联标签列表
     */
    private List<DataCollectionRequirementDetailDataTag> relatedTags;
}
