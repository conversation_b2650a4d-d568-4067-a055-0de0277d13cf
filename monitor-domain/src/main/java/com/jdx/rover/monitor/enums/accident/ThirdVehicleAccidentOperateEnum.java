package com.jdx.rover.monitor.enums.accident;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: 三方车事故操作枚举
 * @author: do<PERSON><PERSON><PERSON>
 * @date: 2025/8/21
 */

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ThirdVehicleAccidentOperateEnum {

    CREATED("CREATED","创建"),
    TECHNICAL_SUPPORT_EDIT("TECHNICAL_SUPPORT_EDIT", "远程安全员编辑"),
    SAFETY_GROUP_EDIT("SAFETY_GROUP_EDIT","安全组编辑"),
    RECOVER("RECOVER", "恢复"),
    REPAIR("REPAIR", "维修"),
    ;

    private final String value;

    private final String name;

    public static String getNameByValue(String value) {
        for (ThirdVehicleAccidentOperateEnum thirdVehicleAccidentOperateEnum : ThirdVehicleAccidentOperateEnum.values()) {
            if (thirdVehicleAccidentOperateEnum.getValue().equals(value)) {
                return thirdVehicleAccidentOperateEnum.getName();
            }
        }
        return null;
    }
}
