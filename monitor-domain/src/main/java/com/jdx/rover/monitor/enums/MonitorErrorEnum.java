/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 错误码枚举
 * A开头 客户端错误
 * B开头 服务端错误
 * C开头 第三方错误
 * 01 主数据 02 调度
 * 03 监控服务端
 * 06 不可通行区域
 * 07 远程指令
 *
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MonitorErrorEnum {
  // OK
  OK("0000", "正常"),

  // A开头,客户端错误
  ERROR_CALL_CHECK_PARAM("A001001", "参数校验异常"),
  // B开头,服务端错误

  // 3开头，调度业务
  ERROR_SCHEDULE_ABSENT("B003001", "车辆调度不存在"),
  ERROR_SCHEDULE_STOP_ABSENT("B003002", "车辆调度停靠点不存在"),
  ERROR_SCHEDULE_CALL_SERVICE("B003005", "车辆调度服务异常"),

  ERROR_VEHICLE_ABSENT("B004001", "车辆不存在"),

  NOT_EMERGENCY_STOP("B005001", "车辆未急停"),
  OPERATOR_MISMATCH_TAKEOVER("B005002", "操作人不是接管人"),

  // 6开头,工单错误
  ERROR_ISSUE_ABSENT("B006001", "工单不存在"),
  ERROR_ISSUE_PROSESS("B006002", "工单处理中"),
  ERROR_ISSUE_CRRATE("B006003", "工单处理错误"),
  ERROR_ISSUE_STATE("B006004", "工单未受理"),

  ERROR_VEHICLE_WITHIN_CONTROL("B007001", "车辆正在接管中"),
  ERROR_USER_PHONE_ABSENT("B007002", "用户不存在"),
  ERROR_VEHICLE_CONTROL_OFFLINE("B007003", "连接已断开，请检查网络！"),


  CLIENT_WEBSOCKET_SEND_REALTIME("B080701", "客户端websocket发送实时接口失败"),
  CLIENT_WEBSOCKET_SEND_MAPSCHEDULE("B080702", "客户端websocket发送地图调度失败"),
  CLIENT_WEBSOCKET_REDIS_TIMEOUT("B080703", "客户端websocket redis操作超时"),
  CLIENT_WEBSOCKET_SEND_CLOUDMAP("B080704", "客户端websocket发送点云地图失败"),

  // 9 开头 地图页错误
  ERROR_CALL_MAP_VEHICLE_ANSENT("B009001", "该车地图信息缺失，请选择其他车号"),

  // 11开头 监控小程序错误
  ERROR_MINI_MONITOR_USER_ABSENT("B011001", "用户不存在"),
  ERROR_MINI_MONITOR_STATION_ABSENT("B011002", "站点不存在"),
  ERROR_MINI_MONITOR_STOP_ABSENT("B011003", "停靠点不存在"),
  ERROR_MINI_MONITOR_VEHICLE_ABSENT("B011004", "车辆不存在"),
  ERROR_MINI_MONITOR_CALL_SERVICE("B011005", "服务异常"),
  ERROR_MINI_MONITOR_ISSUE_AVAILABLE("B011006", "存在未完成工单"),
  ERROR_MINI_MONITOR_ORDER_ABSENT("B011007", "未查询到相关订单"),
  ERROR_MINI_MONITOR_STATION_USER_ATTENTION("B011008", "站点负责人无法关闭碰撞通知"),

  // 12开头 中控远驾错误
  ERROR_REPEAT_OPERATION("B031200", "服务繁忙，请重新操作"),
  ERROR_COCKPIT_OCCUPIED("B031201", "当前座席已被占用，请重新选择"),
  ERROR_USER_TAKING_OVER("B031202", "请先退出接管再操作"),
  ERROR_USER_ENTERED("B031203", "当前用户已进入座席，请先退出座席操作"),
  ERROR_USER_EXITED("B031204", "当前用户已退出座席，请刷新页面重试"),
  ERROR_COCKPIT_HAS_ISSUE("B031205", "请先完成全部工单后再操作"),
  ERROR_USER_COCKPIT_MATCH("B031206", "当前用户与座席不匹配，请刷新页面重试"),
  ERROR_COCKPIT_VEHICLE_BIND("B031207", "车辆%s正在被接管中，无法解除绑定"),
  ERROR_COCKPIT_TEAM_DISABLE("B031208", "座席团队已被停用"),
  ERROR_COCKPIT_VEHICLE_REPEAT_BIND("B031209", "绑定车辆重复,无法绑定"),

  //13开头 小程序错误
  ERROR_MOBILE_REPEAT_OPERATION("B031300", "服务繁忙，请重新操作"),
  ERROR_MESSAGE_NOT_EXIST("B031301", "卡片不存在,请刷新页面重试"),
  ERROR_ACCIDENT_ACCEPTED("B031302", "事故已经被跟进"),
  ERROR_ACCIDENT_NOT_OWNER("B031303", "与事故跟进人不符"),
  ERROR_MESSAGE_ID_ACCIDENT_NO_MATCH("B031304", "卡片与事故编号不符"),
  ERROR_FREE_DRIVE_SYSTEM_STATUS("B031305", "车辆离线，暂不支持自由跑行！"),
  ERROR_FREE_DRIVE_BUSINESS_STATUS("B031306", "车辆有任务，暂不支持自由跑行！"),
  ERROR_FREE_DRIVE_REPAIR_STATUS("B031307", "车辆存在影响运营的维修单！"),
  ERROR_LOGIN_VEHICLE_OFFLINE("B031308", "车辆离线，暂不支持远程登录！"),
  ERROR_COMMAND_SEND("B031309", "指令下发失败！"),
  ERROR_ACCIDENT_FINISH("B031310", "事故已无需处理"),
  ERROR_POWER_MANAGER_ABSENT("B031311", "当前车辆暂不支持该功能！"),
  ERROR_SHUTDOWN_VEHICLE_OFFLINE("B031312", "当前车辆已关机，请勿重复关机！"),
  ERROR_POWER_ON_VEHICLE_ON("B031313", "当前车辆已开机，请勿重复开机！"),
  ERROR_TASK_NOT_EDITABLE("B031314", "勘查任务无法编辑"),
  ERROR_TASK_NOT_EXIST("B031315", "勘查任务不存在"),
  ERROR_TASK_CANNOT_DELETE("B031316", "勘查任务无法删除"),
  ERROR_TASK_CANNOT_PROCESS("B031317", "正有用户（%s）勘查此路线"),
  ERROR_DUPLICATE_FILE_KEY("B031318", "附件名称重复，请修改后重新上传"),
  ERROR_TASK_STATUS_CHANGE("B031319", "勘查任务状态发生变更，请刷新后重试"),
  ERROR_H5_GET_SITE_ID("B031320", "【物流网关】上下文获取siteId失败"),
  ERROR_H5_GET_SITE_VEHICLE_LIST("B031321", "获取站点下车辆列表失败"),
  ERROR_H5_GET_USER_ACCOUNT("B031322", "【物流网关】上下文获取userAccount失败"),
  ERROR_PDU_CONNECT("B031323", "请查看车辆的刀闸是否已开启，或该车不支持该功能！"),

  //14开头 监控事故相关
  ERROR_ACCIDENT_NOT_EXIST("B031400", "事故不存在"),

  //15开头 地图采集相关
  ERROR_ACTION_TYPE("B031501", "错误的操作类型，目前不支持"),
  ERROR_SEND_COMMAND("B031502", "端云指令下发失败"),
  ERROR_COLLECTION_STATUS("B031503", "采集状态不符，不允许操作"),
  ERROR_TASK_STATUS("B031504", "任务状态不符，不允许操作"),
  ERROR_ASSOCIATE_VEHICLE_EXIST("B031505", "当前任务状态不支持认领，请重新选择其他任务"),
  ERROR_ASSOCIATE_VEHICLE_NOT_RIGHT("B031506", "关联车辆不正确,无法操作"),
  ERROR_ASSOCIATE_VEHICLE_HAVE_TASK("B031507", "关联车辆已有任务,无法操作"),
  ERROR_TASK("B031508", "任务不存在"),
  ERROR_REALTIME_INFO("B031509", "车辆实时信息不存在，请联系研发处理"),
  ERROR_SYSTEM_STATE("B031510", "车辆系统状态不符，不允许操作"),
  ERROR_VEHICLE_STATE("B031511", "车辆状态不符，不允许操作"),
  ERROR_ZERO_SPEED("B031512", "车速不为0，不允许操作"),
  ERROR_TAKEOVER_STATE("B031513", "车辆已被其他接管，不允许操作"),
  ERROR_SCHEDULE_STATE("B031514", "车辆存在调度，不允许操作"),
  ERROR_COLLECTION_MODE("B031515", "车辆未处于采图模式，不允许操作"),
  ERROR_VEHICLE_STATUS_INFO("B031516", "车辆状态信息不存在，请联系研发处理"),
  ERROR_CANCEL_VEHICLE_SOLIDIFIED_TASK_FAIL("B031517", "取消车辆固化任务失败"),

  // 16开头 数采车相关
  ERROR_COLLECTION_REQUIREMENT_NOT_EXIST("B031600", "采集需求不存在"),
  ERROR_COLLECTION_VEHICLE_TASK_NOT_EXIST("B031601", "车辆采集任务不存在"),
  ERROR_COLLECTION_SCENE_NOT_EXIST("B031602", "采集场景不存在"),
  ERROR_COLLECTION_VEHICLE_NOT_EXIST("B031603", "车辆非数采车，不允许操作"),
  ERROR_COLLECTION_VEHICLE_HAS_USER("B031604", "该车已被用户%s登录，不允许操作！"),


  // C开头,三方调用错误
  ERROR_CALL_SERVICE("C021001", "服务端远程调用服务异常"),
  ERROR_CALL_UPLOAD("C021002", "服务端远程调用上传异常"),
  ERROR_CALL_DOWNLOAD("C021003", "服务端远程调用下载异常"),

  ERROR_CALL_IMPASSABLE_REST_SERVICE("C006001", "服务端远程调用不可通行区域服务异常"),
  ERROR_CALL_IMPASSABLE_PARAM_SERVICE("C006002", "服务端远程调用不可通行区域参数异常"),
  ERROR_CALL_IMPASSABLE_RESOLVE_SERVICE("C006003", "服务端远程调用不可通行区域解析异常"),

 ;
  /**
   * 状态码
   */
  private String code;

  /**
   * 提示消息
   */
  private String message;
}