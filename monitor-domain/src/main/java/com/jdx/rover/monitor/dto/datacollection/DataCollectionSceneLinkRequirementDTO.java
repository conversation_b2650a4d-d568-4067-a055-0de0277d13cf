/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.datacollection;

import lombok.Data;

import java.util.List;

/**
 * 数采车场景关联需求
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionSceneLinkRequirementDTO {

    /**
     * 需求标识
     */
    private Integer requirementId;

    /**
     * 描述
     */
    private String description;

    /**
     * 需求标签名称列表
     */
    private List<String> tagNameList;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;

    /**
     * 重合度，100以内整数
     */
    private Integer overlap;

    /**
     * 需求是否已关联该场景
     */
    private Boolean hasAssociated;

}