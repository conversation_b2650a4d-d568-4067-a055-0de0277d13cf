/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import cn.hutool.extra.spring.SpringUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * Jmq消息发送主题枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum JmqProducerTopicEnum {

  /**
   * 座席实时状态变化
   */
  MONITOR_COCKPIT_STATUS_CHANGE("MONITOR_COCKPIT_STATUS_CHANGE", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_cockpit_realtime_status_change")),
  /**
   * 车辆远程指令记录日志
   */
  MONITOR_REMOTE_COMMAND_LOG("MONITOR_REMOTE_COMMAND_LOG", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_remote_command_log")),
  /**
   * 监控影子事件主题
   */
  MONITOR_SHADOW_TRACKING_EVENT("MONITOR_SHADOW_TRACKING_EVENT", SpringUtil.getProperty("monitor.jmq.provider.topic.shadow_tracking_event")),
  /**
   * 监控影子JIRA事件
   */
  MONITOR_SHADOW_JIRA_EVENT("MONITOR_SHADOW_JIRA_EVENT", SpringUtil.getProperty("monitor.jmq.provider.topic.shadow_jira_event")),
  /**
   * 监控PNC规划
   */
  MONITOR_PNC_ROUTE("MONITOR_PNC_ROUTE", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_pnc_route")),
  /**
   * 远程工具终端命令
   */
  SERVER_WEB_TERMINAL_COMMAND_DOWN("SERVER_WEB_TERMINAL_COMMAND_DOWN", SpringUtil.getProperty("monitor.jmq.provider.topic.server_web_terminal_command_down")),
  /**
   * 远程命令下发
   */
  MONITOR_VEHICLE_COMMAND_OPERATION("MONITOR_VEHICLE_COMMAND_OPERATION", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_vehicle_remote_command_operation")),
  /**
   * 远程控制命令
   */
  MONITOR_REMOTE_CONTROL_COMMAND("MONITOR_REMOTE_CONTROL_COMMAND", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_remote_control_command")),
  /**
   * 手动触发车辆报警
   */
  MANUAL_VEHICLE_ALARM("MANUAL_VEHICLE_ALARM", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_manual_vehicle_alarm")),
  /**
   * 监控查看订单详情
   */
  MONITER_VIEW_ORDER_DETAIL("MONITER_VIEW_ORDER_DETAIL", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_view_order_detail")),
  /**
   * 监控车辆事件变化
   */
  MONITOR_VEHICLE_EVENT_CHANGE("MONITOR_VEHICLE_EVENT_CHANGE", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_vehicle_change_event")),
  /**
   * 监控单个车辆的实时数据变化
   */
  MONITOR_SINGLE_VEHICLE_REALTIME("MONITOR_SINGLE_VEHICLE_REALTIME", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_single_vehicle_realtime")),
  /**
   * 监控单个车辆调度变化
   */
  MONITOR_SINGLE_VEHICLE_SCHEDULE("MONITOR_SINGLE_VEHICLE_SCHEDULE", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_single_vehicle_schedule")),
  /**
   * 监控事故流事件
   */
  MONITOR_ACCIDENT_FLOW_EVENT("MONITOR_ACCIDENT_FLOW_EVENT", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_accident_flow_event")),
  /**
   * 地图车辆报警事件
   */
  MAP_VEHICLE_ALARM("MAP_VEHICLE_ALARM", SpringUtil.getProperty("monitor.jmq.provider.topic.map_vehicle_alarm")),
  /**
   * 中控地图采集清除接管消息
   */
  MONITOR_MAP_COLLECTION_TAKEOVER("MONITOR_MAP_COLLECTION_TAKEOVER", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_map_collection_takeover")),
  /**
   * 机器人设备事件变化
   */
  MONITOR_ROBOT_EVENT_CHANGE("MONITOR_ROBOT_EVENT_CHANGE", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_robot_change_event")),
  /**
   * h5接驳用户操作指令
   */
  MONITOR_H5_TRANSPORT_COMMAND("MONITOR_H5_TRANSPORT_COMMAND", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_h5_transport_command")),
  /**
   * 京ME消息卡片回调主题
   */
  MONITOR_JDME_CALLBACK("MONITOR_JDME_CALLBACK", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_jdme_callback")),
  /**
   * 事故信息发送
   */
  MONITOR_ACCIDENT_BASIC_INFO("MONITOR_ACCIDENT_BASIC_INFO", SpringUtil.getProperty("monitor.jmq.provider.topic.monitor_accident_basic_info")),
  ;

  /**
   * <p>
   * 主题类型
   * </p>
   */
  private final String value;

  /**
   * <p>
   * 主题
   * </p>
   */
  private final String topic;

  /**
   * <p>
   * 依据value获取enum
   * </p>
   */
  public static JmqProducerTopicEnum getByValue(final String value) {
    for (JmqProducerTopicEnum itemEnum : JmqProducerTopicEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
