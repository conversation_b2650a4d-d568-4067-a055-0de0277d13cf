package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 事故表
 */
@Data
public class Accident extends BaseModel {

    /**
     * 影子系统编号
     */
    private Integer shadowEventId;

    /**
     * 告警事件编号
     */
    private String alarmNumber;

    /**
     * 事故创建来源
     */
    private String accidentSource;

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 事故描述
     */
    private String accidentDesc;

    /**
     * 事故发生时间
     */
    private Date accidentReportTime;

    /**
     * 开始时间
     */
    private Date accidentStartTime;

    /**
     * 结束时间
     */
    private Date accidentEndTime;

    /**
     * 发生地址
     */
    private String accidentAddress;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * rover版本号
     */
    private String roverVersion;

    /**
     * 行云缺陷编号
     */
    private String bugCode;

    /**
     * 一线跟进人
     */
    private String operationUser;

    /**
     * 技术支持是否处理
     */
    private Integer isTechnicalSupportHandle;

    /**
     * 一线处理状态
     */
    private String operationStatus;

    /**
     * 是否需要安全组
     */
    private Integer isSafetyGroup;

    /**
     * 是否有暂存的事故单
     */
    private Integer isHaveTemporaryIncident;

    /**
     * 缺陷状态
     */
    private String bugStatus;

    /**
     * 缺陷创建时间
     */
    private Date bugCreateTime;

    /**
     * 事故负责人
     */
    private String accidentOwner;

    /**
     * 安全组是否编辑过
     */
    private Integer isSafetyGroupEdit;

    /**
     * 视频截取状态：capturing-截取中;success-截取成功;fail-截取失败
     */
    private String videoCaptureStatus;

    /**
     * 事故创建日期 YYYY-MM-DD格式
     */
    private Date accidentDayTime;

    /**
     * 工单编号
     */
    private String issueNumber;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 是否纳入统计
     */
    private Integer whetherIncludeStatistics;

    /**
     * 恢复时间
     */
    private Date recoverTime;

    /**
     * 事故状态
     */
    private String status;
}
