package com.jdx.rover.monitor.dto.accident;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class GetAccidentPageListDTO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 影子系统编号
     */
    private Integer shadowEventId;

    /**
     * 事故来源
     */
    private String accidentSource;

    /**
     * 事故来源名称
     */
    private String accidentSourceName;

    /**
     * 事故时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accidentTime;

    /**
     * 事故发生时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accidentReportTime;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 一线处理状态
     */
    private String operationStatus;

    /**
     * 一线处理状态名称
     */
    private String operationStatusName;

    /**
     * 一线跟进人
     */
    private String operationUser;

    /**
     * 一线处理方式
     */
    private String operationHandleMethodName;

    /**
     * 是否需要安全组介入
     */
    private String isSafetyGroupName;

    /**
     * 行云缺陷号
     */
    private String bugCode;

    /**
     * 定责事故等级
     */
    private String safetyGroupAccidentLevelName;

    /**
     * 定责事故分类
     */
    private String safetyGroupAccidentTypeName;

    /**
     * 关键问题分析
     */
    private String safetyGroupDescription;

    /**
     * 事故模块
     */
    private String accidentModuleName;

    /**
     * 事故标签
     */
    private String accidentTagName;

    /**
     * 缺陷状态
     */
    private String bugStatusName;

    /**
     * 事故负责人
     */
    private String accidentOwner;

    /**
     * 解决方案
     */
    private String safetyGroupAccidentSolution;

    /**
     * 事故解决情况名称
     */
    private String safetyGroupAccidentResolutionStatusName;

    /**
     * 技术支持处理状态
     */
    private String technicalSupportHandleStatusName;

    /**
     * 工单编号
     */
    private String issueNumber;

    /**
     * 是否纳入事故统计
     */
    private Integer whetherIncludeStatistics;

    /**
     * 是否纳入事故统计名称
     */
    private String whetherIncludeStatisticsName;

    /**
     * 供应商标识
     */
    private String supplier;

    /**
     * 供应商标识名称
     */
    private String supplierName;
}
