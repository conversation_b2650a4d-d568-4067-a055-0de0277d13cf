/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 创建数据采集需求请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class DataCollectionRequirementCreateVO {

    /**
     * 需求编号
     */
    @NotBlank(message = "需求编号不能为空")
    private String requirementNumber;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;
}
