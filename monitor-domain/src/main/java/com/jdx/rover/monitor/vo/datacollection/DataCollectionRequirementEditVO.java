/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import jakarta.validation.Valid;
import java.util.List;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 编辑数据采集需求详情请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class DataCollectionRequirementEditVO {

    /**
     * 需求ID
     */
    @NotNull(message = "需求ID不能为空")
    private Integer requirementId;

    /**
     * 需求说明
     */
    @NotBlank(message = "需求说明不能为空")
    private String description;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;

    /**
     * 关联标签列表
     */
    @Valid
    private List<RequirementDataTagVO> relatedTags;
}
