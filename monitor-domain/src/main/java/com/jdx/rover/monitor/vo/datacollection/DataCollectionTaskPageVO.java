/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.datacollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 地图采集分页查询VO
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionTaskPageVO extends PageVO {

    /**
     * 表示查询任务的开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull
    private Date startTime;

    /**
     * 表示查询任务的结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull
    private Date endTime;

    /**
     * 表示查询任务关联的车辆名称
     */
    private String vehicleName;
}
