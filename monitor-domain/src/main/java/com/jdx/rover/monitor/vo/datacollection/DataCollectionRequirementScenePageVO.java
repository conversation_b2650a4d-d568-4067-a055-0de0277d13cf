/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import com.jdx.rover.common.domain.page.PageVO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据采集需求分页查询请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataCollectionRequirementScenePageVO extends PageVO {

    /**
     * 需求ID
     */
    @NotNull(message = "需求ID不能为空")
    private Integer requirementId;
}
