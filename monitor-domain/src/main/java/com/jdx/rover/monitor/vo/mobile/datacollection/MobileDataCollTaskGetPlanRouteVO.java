/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.mobile.datacollection;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 获取规划路径入参
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Data
public class MobileDataCollTaskGetPlanRouteVO {

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String vehicleName;

    /**
     * 客户端ID，设备唯一编号
     */
    @NotBlank(message = "客户端ID不能为空")
    private String clientId;
}
