/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.mobile.datacollection;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据采集需求列表DTO
 *
 * <AUTHOR>
 * @date 2025/07/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MobileDataCollRequirementListDTO {

    /**
     * 需求ID
     */
    private Integer requirementId;

    /**
     * 需求描述
     */
    private String description;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;

    /**
     * 采集进度，保留两位小数
     */
    private Double progress;
}
