/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 新建数据采集需求请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class AddDataCollectionRequirementVO {

    /**
     * 需求说明
     */
    @NotBlank(message = "需求说明不能为空")
    private String description;

    /**
     * 标签列表
     */
    @Valid
    private List<RequirementDataTagVO> relatedTags;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;
}
