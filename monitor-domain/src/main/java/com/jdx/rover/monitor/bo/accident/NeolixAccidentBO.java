package com.jdx.rover.monitor.bo.accident;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

@Data
public class NeolixAccidentBO {

    @Alias("车牌号")
    private String vehicleName;

    @Alias("站点名称")
    private String stationName;

    @Alias("发生时间")
    private String reportTime;

    @Alias("事故等级")
    private String accidentLevel;

    @Alias("问题描述")
    private String description;

    @Alias("恢复时间")
    private String recoveryTime;

}
