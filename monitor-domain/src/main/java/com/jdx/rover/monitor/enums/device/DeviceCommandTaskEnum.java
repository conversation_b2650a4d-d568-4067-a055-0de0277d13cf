/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 设备指令事件类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum DeviceCommandTaskEnum {

    /**
     * HMI远程跟进
     */
    CMD_OPERATION_FOLLOW("CMD_OPERATION_FOLLOW", "HMI远程跟进"),
    /**
     * 告警推送事件。
     */
    CMD_ALARM_UPDATE("CMD_ALARM_UPDATE", "机器人告警变化"),
    /**
     * 告警推送事件。
     */
    CMD_FORCE_ARRIVED("CMD_FORCE_ARRIVED", "机器人视同到达"),

    /**
     * 远程接管事件。
     */
    CMD_REMOTE_COMMAND("CMD_REMOTE_COMMAND", "机器人远程接管"),
    /**
     * Hmi远程重启事件。
     */
    CMD_REMOTE_HMI_BOOT("CMD_REMOTE_HMI_BOOT", "HMI远程重启"),

    /**
     * 数采车异常状态更新的指令事件。
     */
    VEHICLE_ERROR_UPDATE("VEHICLE_ERROR_UPDATE", "数采车异常推送")
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String  name;

    /**
     * 根据给定的值获取对应的 DeviceCommandTaskEnum 枚举类型。
     */
    public static DeviceCommandTaskEnum getByValue(String value) {
        for (DeviceCommandTaskEnum em : DeviceCommandTaskEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }

}