/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.mobile.datacollection;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 添加规划路径入参
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Data
public class MobileDataCollTaskSavePlanRouteVO {

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String vehicleName;

    /**
     * 客户端ID，设备唯一编号
     */
    @NotBlank(message = "客户端ID不能为空")
    private String clientId;

    /**
     * 点位列表
     */
    @Valid
    @NotEmpty(message = "点位列表不能为空")
    private List<RoutePoint> pointList;

    @Data
    public static class RoutePoint {

        /**
         * 纬度
         */
        @NotNull(message = "纬度不能为空")
        private Double latitude;

        /**
         * 经度
         */
        @NotNull(message = "经度不能为空")
        private Double longitude;
    }
}
