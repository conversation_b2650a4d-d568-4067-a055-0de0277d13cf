package com.jdx.rover.monitor.bo.accident;

import cn.hutool.core.util.StrUtil;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.StatusEnum;
import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.monitor.dto.accident.GetAccidentPageListDTO;
import com.jdx.rover.monitor.enums.accident.AccidentSourceEnum;
import com.jdx.rover.monitor.enums.mobile.*;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class AccidentBO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 影子系统编号
     */
    private Integer shadowEventId;

    /**
     * 事故来源
     */
    private String accidentSource;

    /**
     * 事故时间
     */
    private Date accidentTime;

    /**
     * 事故发生时间
     */
    private Date accidentReportTime;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 一线处理状态名称
     */
    private String operationStatus;

    /**
     * 一线跟进人
     */
    private String operationUser;

    /**
     * 一线处理方式
     */
    private String operationHandleMethod;

    /**
     * 是否需要安全组介入
     */
    private Integer isSafetyGroup;

    /**
     * 行云缺陷号
     */
    private String bugCode;

    /**
     * 定责事故等级
     */
    private String safetyGroupAccidentLevel;

    /**
     * 定责事故分类
     */
    private String safetyGroupAccidentType;

    /**
     * 关键问题分析
     */
    private String safetyGroupDescription;

    /**
     * 事故模块
     */
    private Long accidentModule;

    /**
     * 事故标签
     */
    private Long accidentTag;

    /**
     * 缺陷状态
     */
    private String bugStatus;

    /**
     * 事故负责人
     */
    private String accidentOwner;

    /**
     * 解决方案
     */
    private String safetyGroupAccidentSolution;

    /**
     * 事故解决情况
     */
    private String safetyGroupAccidentResolutionStatus;

    /**
     * 技术支持处理状态
     */
    private Integer isTechnicalSupportHandle;

    /**
     * 工单编号
     */
    private String issueNumber;

    /**
     * 是否纳入事故统计
     */
    private Integer whetherIncludeStatistics;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 新事故等级
     */
    private String newAccidentLevel;

    /**
     * 将AccidentBO对象转换为GetAccidentPageListDTO对象。
     *
     * @param accidentBO 转换的源对象
     * @return 转换后的目标对象
     */
    public GetAccidentPageListDTO convertBoToDto(AccidentBO accidentBO) {
        GetAccidentPageListDTO result = new GetAccidentPageListDTO();
        result.setAccidentNo(accidentBO.getAccidentNo());
        result.setShadowEventId(accidentBO.getShadowEventId());
        result.setAccidentSource(accidentBO.getAccidentSource());
        result.setAccidentSourceName(AccidentSourceEnum.getNameByValue(accidentBO.getAccidentSource()));
        result.setAccidentTime(accidentBO.getAccidentTime());
        result.setAccidentReportTime(accidentBO.getAccidentReportTime());
        result.setVehicleName(accidentBO.getVehicleName());
        result.setOperationStatus(accidentBO.getOperationStatus());
        result.setOperationStatusName(AccidentOperationStatusEnum.getNameByValue(accidentBO.getOperationStatus()));
        result.setOperationUser(accidentBO.getOperationUser());
        if (StrUtil.isNotBlank(accidentBO.getOperationHandleMethod())) {
            result.setOperationHandleMethodName(AccidentHandleMethodEnum.getNameByValue(accidentBO.getOperationHandleMethod()));
        } else if (AccidentOperationStatusEnum.COMPLETED.getValue().equals(accidentBO.getOperationStatus())) {
            result.setOperationHandleMethodName(AccidentOperateEnum.REJECT.getName());
        }
        result.setIsSafetyGroupName(AccidentIsSatefyEnum.getNameByValue(accidentBO.getIsSafetyGroup()));
        result.setBugCode(accidentBO.getBugCode());
        result.setSafetyGroupAccidentLevelName(NewAccidentLevelEnum.getNameByValue(accidentBO.getNewAccidentLevel()));
        result.setSafetyGroupAccidentTypeName(SafetyGroupAccidentTypeEnum.getNameByValue(accidentBO.getSafetyGroupAccidentType()));
        result.setSafetyGroupAccidentResolutionStatusName(AccidentResolutionStatusEnum.getNameByValue(accidentBO.getSafetyGroupAccidentResolutionStatus()));
        result.setTechnicalSupportHandleStatusName(AccidentTechnicalStatusEnum.getNameByValue(accidentBO.getIsTechnicalSupportHandle()));
        result.setSafetyGroupDescription(accidentBO.getSafetyGroupDescription());
        result.setBugStatusName(StatusEnum.getAliasByValue(accidentBO.getBugStatus()));
        result.setAccidentOwner(accidentBO.getAccidentOwner());
        result.setSafetyGroupAccidentSolution(accidentBO.getSafetyGroupAccidentSolution());
        Long accidentTag = accidentBO.getAccidentTag();
        //设置事故标签
        List<String> accidentTagNameList = new ArrayList<>();
        if (accidentTag != null && accidentTag > 0) {
            for (AccidentTagEnum tagEnum : AccidentTagEnum.values()) {
                Integer value = tagEnum.getValue();
                if ((accidentTag & 1L << value) > 0) {
                    accidentTagNameList.add(tagEnum.getName());
                }
            }
        }
        result.setAccidentTagName(CollectionUtils.isEmpty(accidentTagNameList) ? null : String.join(",", accidentTagNameList));
        //设置事故模块
        Long accidentModule = accidentBO.getAccidentModule();
        List<String> accidentModuleNameList = new ArrayList<>();
        if (accidentModule != null && accidentModule > 0) {
            for (AccidentModuleEnum moduleEnum : AccidentModuleEnum.values()) {
                Integer value = moduleEnum.getValue();
                if ((accidentModule & 1L << value) > 0) {
                    accidentModuleNameList.add(moduleEnum.getName());
                }
            }
        }
        result.setAccidentModuleName(CollectionUtils.isEmpty(accidentModuleNameList) ? null : String.join(",", accidentModuleNameList));
        result.setIssueNumber(accidentBO.getIssueNumber());
        result.setWhetherIncludeStatistics(accidentBO.getWhetherIncludeStatistics());
        result.setWhetherIncludeStatisticsName(YesOrNoEnum.getNameByValue(accidentBO.getWhetherIncludeStatistics()));
        result.setSupplier(accidentBO.getSupplier());
        result.setSupplierName(SupplierEnum.getNameByValue(accidentBO.getSupplier()));
        return result;
    }
}
