/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.enums.datacollection;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据采集任务状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Getter
@AllArgsConstructor
public enum DataCollectionTaskStatusEnum {

    /**
     * 待质检
     */
    STANDBY("STANDBY", "待质检"),
    /**
     * 质检完成
     */
    FINISHED("FINISHED", "质检完成")

    ;


    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return DataCollectionSceneStatusEnum
     */
    public static DataCollectionTaskStatusEnum getByValue(String value) {
        for (DataCollectionTaskStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

}