/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.datacollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 数采车质检任务分页响应
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionTaskPageDTO {

    /**
     * 任务编号
     */
    private Integer id;

    /**
     * 任务所属采集日期，yyyy-MM-dd
     */
    private String date;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 表示任务的开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 表示任务的结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 采集用户erp
     */
    private String collectionUser;

    /**
     * 任务采集城市
     */
    private String address;

    /**
     * 采集任务状态
     */
    private String status;

}