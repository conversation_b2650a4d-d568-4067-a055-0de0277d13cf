/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * The enumerate alarm event type and solution type of vehicle.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AlarmEventSolutionEnum {
    /**
     * <p>
     * The enumerate alarm event type and solution type of vehicle.
     * </p>
     */
    UNKNOWN("UNKNOWN", "UNKNOWN", "", ""),
    VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", SolutionEnum.ON_SITE_REPAIR.getSolutionType(), "按钮停车", SolutionEnum.ON_SITE_REPAIR.getSolutionTypeName()),
    LOW_BATTERY("LOW_BATTERY", SolutionEnum.BACK_TO_CHARGE.getSolutionType(), "电量低", SolutionEnum.BACK_TO_CHARGE.getSolutionTypeName()),
    VEHICLE_STOP_FATAL("VEHICLE_STOP_FATAL", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "异常停车", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    VEHICLE_STOP_GUARDIAN("VEHICLE_STOP_GUARDIAN", SolutionEnum.ON_SITE_REPAIR.getSolutionType(), "安全接管停车", SolutionEnum.ON_SITE_REPAIR.getSolutionTypeName()),
    VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "遇阻停车", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    VEHICLE_CRASH("VEHICLE_CRASH", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "碰撞", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    LOW_PRESSURE("LOW_PRESSURE", SolutionEnum.ON_SITE_REPAIR.getSolutionType(), "胎压异常", SolutionEnum.ON_SITE_REPAIR.getSolutionTypeName()),
    SENSOR_ERROR("SENSOR_ERROR", SolutionEnum.ON_SITE_REPAIR.getSolutionType(), "传感器异常", SolutionEnum.ON_SITE_REPAIR.getSolutionTypeName()),
    OPERATION_ALARM("OPERATION_ALARM", SolutionEnum.ISSUE_HANDLE.getSolutionType(), "运营报警", SolutionEnum.WAIT_FOR_RESCUE.getSolutionTypeName()),
    VEHICLE_STOP_TRAFFICLIGHT_FAIL("VEHICLE_STOP_TRAFFICLIGHT_FAIL", SolutionEnum.PASS_INTERSECTION.getSolutionType(), "感知看灯失败", SolutionEnum.PASS_INTERSECTION.getSolutionTypeName()),
    VEHICLE_STOP_INTERSECTION_STUCK("VEHICLE_STOP_INTERSECTION_STUCK", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "路口遇阻", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    VEHICLE_STOP_TRAFFICLIGHT_TAKEUP("VEHICLE_STOP_TRAFFICLIGHT_TAKEUP", SolutionEnum.PASS_INTERSECTION.getSolutionType(), "看灯点被占", SolutionEnum.PASS_INTERSECTION.getSolutionTypeName()),
    VEHICLE_STOP_TRAFFICLIGHT_ADJUST("VEHICLE_STOP_TRAFFICLIGHT_ADJUST", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "看灯规划失败", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    VEHICLE_STOP_STOP_TAKEUP("VEHICLE_STOP_STOP_TAKEUP", SolutionEnum.AS_ARRIVED.getSolutionType(), "停靠点被占", SolutionEnum.AS_ARRIVED.getSolutionTypeName()),
    VEHICLE_STOP_STOP_ADJUST("VEHICLE_STOP_STOP_ADJUST", SolutionEnum.AS_ARRIVED.getSolutionType(), "停靠规划失败", SolutionEnum.AS_ARRIVED.getSolutionTypeName()),
    PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", SolutionEnum.PASS_INTERSECTION.getSolutionType(), "无保护左转", SolutionEnum.PASS_INTERSECTION.getSolutionTypeName()),
    BOOT_FAIL("BOOT_FAIL", SolutionEnum.POWER_RESTART.getSolutionType(), "开机失败", SolutionEnum.POWER_RESTART.getSolutionTypeName()),
    BOOT_TIMEOUT("BOOT_TIMEOUT", SolutionEnum.WAIT_FOR_RESCUE.getSolutionType(), "开机过久", SolutionEnum.EMERGENCY_TREATMENT.getSolutionTypeName()),
    BOOT_ABNORMAL("BOOT_ABNORMAL", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "异常重启", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", SolutionEnum.POWER_RESTART.getSolutionType(), "异常重启失败", SolutionEnum.POWER_RESTART.getSolutionTypeName()),
    GATE_STUCK("GATE_STUCK", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "过门遇阻", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    PARK_HARD("PARK_HARD", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "停靠困难", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    PRESSURE_SHARP_DECREASE("PRESSURE_SHARP_DECREASE", SolutionEnum.CALL_OPERATION.getSolutionType(), "胎压骤减", SolutionEnum.CALL_OPERATION.getSolutionTypeName()),
    CPU_HIGH_TEMPERATURE("CPU_HIGH_TEMPERATURE", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "CPU温度过高", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    LOCALIZATION_ERROR("LOCALIZATION_ERROR", SolutionEnum.LOCALIZATION_RESET.getSolutionType(), "定位异常", SolutionEnum.LOCALIZATION_RESET.getSolutionTypeName()),
    ROUTING_PLAN_FAIL("ROUTING_PLAN_FAIL", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "Routing规划失败", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),

    ATTENTION_REGION_WARN("ATTENTION_REGION_WARN", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "关注预警", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    PARKING_REGION_WARN("PARKING_REGION_WARN", SolutionEnum.REMOTE_CONTROL.getSolutionType(), "停车预警", SolutionEnum.REMOTE_CONTROL.getSolutionTypeName()),
    GUARDIAN_LOST("GUARDIAN_LOST", SolutionEnum.RESTART_MCU.getSolutionType(), "失联", SolutionEnum.RESTART_MCU.getSolutionTypeName()),

    // 人工触发告警
    MANUAL_REPORT("MANUAL_REPORT", SolutionEnum.ISSUE_HANDLE.getSolutionType(), "人工告警", SolutionEnum.WAIT_FOR_RESCUE.getSolutionTypeName()),
    BUG_REPORT("BUG_REPORT", SolutionEnum.ISSUE_HANDLE.getSolutionType(), "问题提报", SolutionEnum.ISSUE_HANDLE.getSolutionTypeName()),

    // 工单调度事件
    SCHEDULE_SET_OUT("SCHEDULE_SET_OUT", SolutionEnum.ISSUE_HANDLE.getSolutionType(), "去程", SolutionEnum.ISSUE_HANDLE.getSolutionTypeName()),
    SCHEDULE_RETURN("SCHEDULE_RETURN", SolutionEnum.ISSUE_HANDLE.getSolutionType(), "返程", SolutionEnum.ISSUE_HANDLE.getSolutionTypeName()),

    // 车身域链路
    RADAR_WARNING("RADAR_WARNING", SolutionEnum.REPORT_REPAIR.getSolutionType(), "雷达预警", SolutionEnum.REPORT_REPAIR.getSolutionTypeName()),
    RADAR_FAILURE("RADAR_FAILURE", SolutionEnum.PULL_OVER.getSolutionType(), "雷达故障", SolutionEnum.PULL_OVER.getSolutionTypeName()),
    AUDIBLE_AND_VISUAL_ALARM_FAILURE("AUDIBLE_AND_VISUAL_ALARM_FAILURE", SolutionEnum.REPORT_REPAIR.getSolutionType(), "声光电故障", SolutionEnum.REPORT_REPAIR.getSolutionTypeName()),
    EDGE_SENSOR_FAILURE("EDGE_SENSOR_FAILURE", SolutionEnum.REPORT_REPAIR.getSolutionType(), "触边故障", SolutionEnum.REPORT_REPAIR.getSolutionTypeName()),
    BATTERY_FAILURE("BATTERY_FAILURE", SolutionEnum.CONTACT_RD.getSolutionType(), "电池故障", SolutionEnum.CONTACT_RD.getSolutionTypeName()),
    BRAKE_FAILURE("BRAKE_FAILURE", SolutionEnum.REPORT_REPAIR.getSolutionType(), "刹车故障", SolutionEnum.REPORT_REPAIR.getSolutionTypeName()),
    PARKING_BRAKE_FAILURE("PARKING_BRAKE_FAILURE", SolutionEnum.REPORT_REPAIR.getSolutionType(), "驻车故障", SolutionEnum.REPORT_REPAIR.getSolutionTypeName()),
    STEERING_EPS_FAILURE("STEERING_EPS_FAILURE", SolutionEnum.REPORT_REPAIR.getSolutionType(), "转向EPS故障", SolutionEnum.REPORT_REPAIR.getSolutionTypeName()),
    ELECTRONIC_CONTROL_MOTOR_FAILURE("ELECTRONIC_CONTROL_MOTOR_FAILURE", SolutionEnum.CONTACT_RD.getSolutionType(), "电控、电机故障", SolutionEnum.CONTACT_RD.getSolutionTypeName()),
    SIM_CARD_ABNORMAL("SIM_CARD_ABNORMAL", SolutionEnum.CONTACT_RD.getSolutionType(), "SIM卡异常", SolutionEnum.CONTACT_RD.getSolutionTypeName()),
    CONNECTIVITY_FAILURE("CONNECTIVITY_FAILURE", SolutionEnum.PULL_OVER_AND_RESTART_OR_CONTACT_STAFF.getSolutionType(), "连通性失败", SolutionEnum.PULL_OVER_AND_RESTART_OR_CONTACT_STAFF.getSolutionTypeName()),
    DOMAIN_CONTROLLER_ALARM("DOMAIN_CONTROLLER_ALARM", SolutionEnum.PULL_OVER_AND_CONTACT_HARDWARE.getSolutionType(), "域控告警", SolutionEnum.PULL_OVER_AND_CONTACT_HARDWARE.getSolutionTypeName()),
    TIRE_PRESSURE_SENSOR_OFFLINE("TIRE_PRESSURE_SENSOR_OFFLINE", SolutionEnum.REPORT_REPAIR.getSolutionType(), "胎压传感器离线", SolutionEnum.REPORT_REPAIR.getSolutionTypeName()),
    ;

    /**
     * <p>
     * The alarm event type of vehicle.
     * </p>
     */
    private String alarmEventType;

    /**
     * <p>
     * The solution type of vehicle.
     * </p>
     */
    private String solutionType;

    /**
     * <p>
     * The alarm event type name of vehicle.
     * </p>
     */
    private String alarmEventTypeName;

    /**
     * <p>
     * The solution type name of vehicle.
     * </p>
     */
    private String solutionTypeName;

    public static AlarmEventSolutionEnum getByAlarmEventType(String alarmEventType) {
        for (AlarmEventSolutionEnum em : AlarmEventSolutionEnum.values()) {
            if (Objects.equals(alarmEventType, em.getAlarmEventType())) {
                return em;
            }
        }
        return UNKNOWN;
    }

    public static AlarmEventSolutionEnum getBySolutionType(String solutionType) {
        for (AlarmEventSolutionEnum em : AlarmEventSolutionEnum.values()) {
            if (Objects.equals(solutionType, em.getSolutionType())) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
