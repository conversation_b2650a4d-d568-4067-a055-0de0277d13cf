/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.enums.datacollection;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据采集需求状态枚举
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Getter
@AllArgsConstructor
public enum DataCollectionStatusEnum {

    /**
     * 进行中
     */
    ONGOING("ONGOING", "进行中"),

    /**
     * 已完成
     */
    FINISHED("FINISHED", "已完成"),

    /**
     * 已关闭
     */
    CLOSED("CLOSED", "已关闭");

    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return DataCollectionStatusEnum
     */
    public static DataCollectionStatusEnum getByValue(String value) {
        for (DataCollectionStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据值获取描述
     *
     * @param value 状态值
     * @return String
     */
    public static String getDescriptionByValue(String value) {
        DataCollectionStatusEnum status = getByValue(value);
        return status != null ? status.getDescription() : null;
    }
}
