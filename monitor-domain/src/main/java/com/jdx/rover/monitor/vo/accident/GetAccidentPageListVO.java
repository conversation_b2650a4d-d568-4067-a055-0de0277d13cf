package com.jdx.rover.monitor.vo.accident;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GetAccidentPageListVO extends PageVO {

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 一线处理状态
     */
    private String operationStatus;

    /**
     * 是否需要安全组介入
     */
    private Boolean isSafetyGroup;

    /**
     * 定责事故等级
     */
    private List<String> safetyGroupAccidentLevelList;

    /**
     * 定责事故分类
     */
    private List<String> safetyGroupAccidentTypeList;

    /**
     * 缺陷状态
     */
    private List<String> bugStatusList;

    /**
     * 事故模块
     */
    private List<Integer> accidentModuleList;

    /**
     * 事故标签
     */
    private List<Integer> accidentTagList;

    /**
     * 事故解决情况
     */
    private String safetyGroupAccidentResolutionStatus;

    /**
     * 技术支持处理情况
     */
    private Integer technicalSupportHandleStatus;

    /**
     * 事故负责人
     */
    private String accidentOwner;

    /**
     * 事故发生开始时间
     */
    private Date accidentReportStartTime;

    /**
     * 事故发生结束时间
     */
    private Date accidentReportEndTime;

    /**
     * 是否纳入事故统计
     */
    private List<Integer> whetherIncludeStatisticsList;

    /**
     * 供应商
     */
    private List<String> supplierList;
}
