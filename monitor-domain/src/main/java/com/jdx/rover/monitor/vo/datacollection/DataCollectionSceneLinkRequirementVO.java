/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 数据采集场景关联请求 VO
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Data
public class DataCollectionSceneLinkRequirementVO {

    /**
     * 场景ID
     */
    @NotNull(message = "场景ID不能为空")
    private Integer sceneId;

    /**
     * 需求ID
     */
    @NotNull(message = "需求ID不能为空")
    private Integer requirementId;

    /**
     * 标识场景与需求是否已关联的标志
     */
    private Boolean linked;
}