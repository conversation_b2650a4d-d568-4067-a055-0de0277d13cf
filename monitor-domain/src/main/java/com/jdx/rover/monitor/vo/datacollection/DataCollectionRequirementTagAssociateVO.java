/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.vo.datacollection;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 需求关联标签请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class DataCollectionRequirementTagAssociateVO {

    /**
     * 需求编号
     */
    @NotBlank(message = "需求编号不能为空")
    private String requirementNumber;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    private String tagName;

    /**
     * 父级标签id
     */
    private Integer parentTagId;

    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 标签需求数量
     */
    @Min(value = 1, message = "标签需求数量必须大于0")
    private Integer count;
}
