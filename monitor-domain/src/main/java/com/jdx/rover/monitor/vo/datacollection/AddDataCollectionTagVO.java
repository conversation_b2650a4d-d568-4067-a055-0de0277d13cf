/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.vo.datacollection;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 新建数据采集标签请求 VO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class AddDataCollectionTagVO {

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    @Size(max = 20, message = "标签名称最大长度为20")
    private String tagName;
}
