/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.datacollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数据采集需求 DTO
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class DataCollectionRequirementDTO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 需求编号
     */
    private String requirementNumber;

    /**
     * 需求状态
     */
    private String status;

    /**
     * 需求状态描述
     */
    private String statusDescription;

    /**
     * 任务进度
     */
    private Double progress;

    /**
     * 必要事项
     */
    private String requiredDetail;

    /**
     * 禁忌事项
     */
    private String forbiddenDetail;

    /**
     * 创建者
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后修改者
     */
    private String modifyUser;

    /**
     * 最近修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 关联的标签列表
     */
    private List<DataCollectionRequirementTagDTO> tags;
}
