package com.jdx.rover.monitor.vo.accident;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.util.List;

/**
 * 安全组编辑事故VO
 */
@Data
public class SafetyGroupEditAccidentVO {

    /**
     * 事故编号
     */
    @NotBlank(message = "事故编号不能为空")
    private String accidentNo;

    /**
     * 定责事故等级
     */
    @NotBlank(message = "事故等级不能为空")
    private String safetyGroupAccidentLevel;

    /**
     * 定责事故分类
     */
    @NotBlank(message = "事故分类不能为空")
    private String safetyGroupAccidentType;

    /**
     * 关键问题分析
     */
    @NotBlank(message = "关键问题分析不能为空")
    private String safetyGroupDescription;

    /**
     * 事故模块
     */
    @NotEmpty(message = "事故模块不能为空")
    private List<Integer> safetyGroupAccidentModuleList;

    /**
     * 事故标签
     */
    @NotEmpty(message = "事故标签不能为空")
    private List<Integer> safetyGroupAccidentTagList;

    /**
     * 挂起原因
     */
    private String safetyGroupAccidentSuspendReason;

    /**
     * 解决方案
     */
    private String safetyGroupAccidentSolution;

    /**
     * 事故报告
     */
    private List<MonitorAccidentAttachmentVO> safetyGroupAccidentReport;

    /**
     * 是否纳入事故统计
     */
    private Integer whetherIncludeStatistics;
}
